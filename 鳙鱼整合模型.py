from joblib import dump, load
import os
import traceback
import pandas as pd
import numpy as np
from sklearn.ensemble import VotingClassifier, RandomForestClassifier
from sklearn.model_selection import GridSearchCV, cross_validate, StratifiedKFold, learning_curve, LeaveOneOut, RepeatedStratifiedKFold
from sklearn.linear_model import LogisticRegression
import lightgbm as lgb
from sklearn.metrics import (make_scorer, f1_score, accuracy_score, precision_score, recall_score, 
                            roc_curve, auc, confusion_matrix, classification_report)
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.pipeline import Pipeline
import json
from datetime import datetime
import argparse
import pkg_resources
import sys
import platform
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

import seaborn as sns
from matplotlib.backends.backend_pdf import PdfPages

from scipy.optimize import differential_evolution
from scipy import stats
from scipy.stats import gmean
import warnings
warnings.filterwarnings('ignore')

import shap

# --- 常量定义 ---
CV_SPLITS = 10
CV_REPEATS = 5
N_JOBS = -1  # 使用所有可用CPU核心
RANDOM_STATE = 42
CLASS_NAMES = ['养殖', '野生']
TOP_N_FEATURES = 20 # SHAP图中显示的特征数量

# --- 英文化翻译映射 ---
SPECIES_MAP = {
    '鳙鱼': 'Bighead Carp',
    '鲢鱼': 'Silver Carp'
}
CLASS_LABEL_MAP = {
    '养殖': 'Culture',
    '野生': 'Wild'
}
# ---------------------

# 动态导入TabPFN
try:
    import torch
except ImportError:
    print("torch模块未安装，将进行安装...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "--no-cache-dir", "torch"])
        import torch

try:
    from tabpfn import TabPFNClassifier
except ImportError:
    print("tabpfn模块未安装，将进行安装...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "--no-cache-dir", "tabpfn"])
        from tabpfn import TabPFNClassifier

# 检测CUDA是否可用
def check_cuda_availability():
    """检查CUDA是否可用并返回适当的设备类型"""
    if torch.cuda.is_available():
        print(f"检测到CUDA，使用GPU: {torch.cuda.get_device_name(0)}")
        torch.cuda.empty_cache()
        return "cuda"
    else:
        print("未检测到CUDA，使用CPU")
        return "cpu"

# CLR转换函数
def clr_transform(data: pd.DataFrame, pseudo_count=1e-6) -> np.ndarray:
    """对DataFrame格式的特征数据应用中心对数比（CLR）转换。"""
    print("应用CLR转换...")
    if not isinstance(data, pd.DataFrame):
        raise TypeError("CLR转换的输入必须是Pandas DataFrame。")
    
    data_with_pseudo = data.values + pseudo_count
    geometric_mean = gmean(data_with_pseudo, axis=1)
    clr_data = np.log(data_with_pseudo / geometric_mean[:, np.newaxis])
    print("CLR转换完成。")
    return clr_data

# 设置字体
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman']
plt.rcParams['axes.unicode_minus'] = True 
plt.rcParams['pdf.fonttype'] = 42
plt.rcParams['svg.fonttype'] = 'none'

# 确保目录存在的辅助函数
def ensure_dir_exists(file_path: str):
    """确保文件路径的目录存在"""
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)


#  核心训练与评估函数 
def train_and_evaluate_models(train_path, val_path, test_path, output_dir, fish_type):
    """
    重构后的主函数，负责加载数据、训练模型、评估和生成可视化。
    该函数不再执行数据划分或SMOTE。
    """
    eng_species_name = SPECIES_MAP.get(fish_type, fish_type)
    
    # 1. 创建输出目录
    visualization_dir = os.path.join(output_dir, 'visualization', eng_species_name)
    model_dir = os.path.join(output_dir, 'models', eng_species_name)
    ensure_dir_exists(os.path.join(visualization_dir, 'dummy.txt'))
    ensure_dir_exists(os.path.join(model_dir, 'dummy.txt'))
    
    # 2. 加载用户提供的数据集
    print("加载预划分的数据集...")
    try:
        train_df = pd.read_csv(train_path)
        val_df = pd.read_csv(val_path)
        test_df = pd.read_csv(test_path)
        print(f"训练集: {train_df.shape}, 验证集: {val_df.shape}, 测试集: {test_df.shape}")
    except FileNotFoundError as e:
        print(f"错误：数据文件未找到 - {e}", file=sys.stderr)
        return
        
    # 3. 数据准备和预处理
    print("准备数据...")
    y_train = train_df['Group']
    X_train_raw = train_df.drop('Group', axis=1)
    
    y_val = val_df['Group']
    X_val_raw = val_df.drop('Group', axis=1)
    
    y_test = test_df['Group']
    X_test_raw = test_df.drop('Group', axis=1)
    
    features = X_train_raw.columns.tolist()
        
        # 标签编码
        le = LabelEncoder()
    y_train = le.fit_transform(y_train)
    y_val = le.transform(y_val)
    y_test = le.transform(y_test)
        class_names = le.classes_
        eng_class_names = [CLASS_LABEL_MAP.get(name, name) for name in class_names]

    # 双数据处理流水线 
    # 流水线1: TabPFN (使用原始数据)
    X_train_tabpfn = X_train_raw.values
    X_val_tabpfn = X_val_raw.values
    
    # 流水线2: 标准模型 (CLR + 标准化)
    print("为标准模型准备数据 (CLR + 标准化)...")
    X_train_clr = clr_transform(X_train_raw.copy())
    X_val_clr = clr_transform(X_val_raw.copy())
    
        scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train_clr)
    X_val_scaled = scaler.transform(X_val_clr)
    
    # 4. 训练模型 (直接在提供的训练集上)
        print("\n训练个体模型...")
        optimized_models = {}
        
        # 随机森林
        print("训练随机森林...")
    rf_model = RandomForestClassifier(random_state=RANDOM_STATE, n_jobs=N_JOBS, class_weight='balanced', n_estimators=200, max_depth=10)
    rf_model.fit(X_train_scaled, y_train)
        optimized_models['Random Forest'] = rf_model
        
        # LightGBM
        print("训练LightGBM...")
    lgbm_model = lgb.LGBMClassifier(random_state=RANDOM_STATE, verbose=-1, force_row_wise=True, objective='binary')
    lgbm_model.fit(X_train_scaled, y_train)
        optimized_models['LightGBM'] = lgbm_model
        
        # 逻辑回归
        print("训练逻辑回归...")
    lr_model = LogisticRegression(random_state=RANDOM_STATE, max_iter=5000, n_jobs=N_JOBS, class_weight='balanced')
    lr_model.fit(X_train_scaled, y_train)
        optimized_models['Logistic Regression'] = lr_model
        
        # TabPFN
        print("\n训练TabPFN模型...")
        try:
            device = check_cuda_availability()
        tabpfn_model = TabPFNClassifier(device=device)
        tabpfn_model.fit(X_train_tabpfn, y_train)
            optimized_models['TabPFN'] = tabpfn_model
        print("TabPFN模型训练完成。")
        except Exception as e:
        print(f"TabPFN模型训练出错: {str(e)}", file=sys.stderr)
            traceback.print_exc()
        
    # 5. 评估模型
        print("\n评估模型...")
    train_metrics, val_metrics, test_metrics = {}, {}, {}
    y_val_probas, y_test_probas = {}, {}
        
        for name, model in optimized_models.items():
        # 选择正确的数据流
        is_tabpfn = (name == 'TabPFN')
        X_train_eval = X_train_tabpfn if is_tabpfn else X_train_scaled
        X_val_eval = X_val_tabpfn if is_tabpfn else X_val_scaled
        
        # 训练集性能
        y_train_pred = model.predict(X_train_eval)
            train_metrics[name] = {
            'accuracy': accuracy_score(y_train, y_train_pred),
            'f1': f1_score(y_train, y_train_pred, average='weighted')
        }
        
        # 验证集性能
        y_val_pred = model.predict(X_val_eval)
            val_metrics[name] = {
            'accuracy': accuracy_score(y_val, y_val_pred),
            'f1': f1_score(y_val, y_val_pred, average='weighted')
        }
        if hasattr(model, 'predict_proba'):
            y_val_probas[name] = model.predict_proba(X_val_eval)[:, 1]

        # 打印性能
        print(f"--- {name} ---")
        print(f"  训练集 - Accuracy: {train_metrics[name]['accuracy']:.4f}, F1-Score: {train_metrics[name]['f1']:.4f}")
        print(f"  验证集 - Accuracy: {val_metrics[name]['accuracy']:.4f}, F1-Score: {val_metrics[name]['f1']:.4f}")

    # 6. 生成可视化分析
    print("\n生成可视化分析...")
    # ... (调用所有plot_*函数，确保它们使用正确的数据) ...
    # 示例：
    if y_val_probas:
        plot_roc_curves(y_val_probas, y_val, visualization_dir, fish_type)

    # 7. 保存模型
    print("\n保存模型...")
    for name, model in optimized_models.items():
        model_path = os.path.join(model_dir, f"{name.replace(' ', '_').lower()}_model.joblib")
        dump(model, model_path)
    
    dump(scaler, os.path.join(model_dir, 'scaler.joblib'))
    dump(le, os.path.join(model_dir, 'label_encoder.joblib'))

    print("\n模型训练与评估流程完成!")


# --- [保留] 所有的可视化函数 (plot_*) 和辅助函数 ---
def get_scorers():
    """定义交叉验证中使用的评估指标"""
    return {
        'f1_macro': make_scorer(f1_score, average='macro')
    }

def save_metrics_to_text(metrics, output_dir, train_metrics=None, optimal_thresholds=None, species_name='鳙鱼'):
    """将模型性能指标和最佳阈值保存为文本文件"""
    print("保存性能指标到文本文件...")
    eng_species = SPECIES_MAP.get(species_name, species_name)
    output_path = os.path.join(output_dir, f"{eng_species}_model_metrics.txt")
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(f"======== {eng_species} Model Performance Evaluation ========\n")
        if train_metrics:
            f.write("\n== Training Set Performance ==\n")
            for name, m in train_metrics.items():
                f.write(f"  {name}: Accuracy={m['accuracy']:.4f}, F1={m['f1']:.4f}\n")
        f.write("\n== Validation Set Performance ==\n")
        for name, m in metrics.items():
            f.write(f"  {name}: Accuracy={m['accuracy']:.4f}, F1={m['f1']:.4f}\n")
        if optimal_thresholds:
            f.write("\n== Optimal Prediction Thresholds ==\n")
            for name, t in optimal_thresholds.items():
                f.write(f"  {name}: {t:.4f}\n")
    print(f"性能指标已保存到 {output_path}")

def plot_roc_curves(y_val_probas, y_val, output_dir, species_name='鳙鱼'):
    """绘制ROC曲线并寻找最佳阈值"""
    print("绘制ROC曲线...")
    eng_species = SPECIES_MAP.get(species_name, species_name)
    plt.figure(figsize=(10, 8))
    optimal_thresholds = {}
    for name, y_prob in y_val_probas.items():
        fpr, tpr, thresholds = roc_curve(y_val, y_prob)
        roc_auc = auc(fpr, tpr)
        J = tpr - fpr
        ix = np.argmax(J)
        best_thresh = thresholds[ix]
        optimal_thresholds[name] = best_thresh
        plt.plot(fpr, tpr, lw=2, label=f'{name} (AUC = {roc_auc:.3f}, Thresh = {best_thresh:.3f})')
    
    plt.plot([0, 1], [0, 1], 'k--', lw=2)
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title(f'ROC Curves for {eng_species} Models')
    plt.legend(loc="lower right")
    plt.grid(True)
    
    output_path = os.path.join(output_dir, f"{eng_species}_roc_curves.png")
    plt.savefig(output_path, dpi=300)
    plt.close()
    print(f"ROC曲线图已保存到 {output_path}")
    return optimal_thresholds
    
# ... 其他所有 plot_* 函数和辅助函数应被复制到这里 ...


# --- [重构] 脚本主入口 ---
if __name__ == "__main__":
    
    # --- 用户配置区 ---
    # 请在此处提供预先划分好的数据集路径
    BASE_DATA_DIR = "C:/Users/<USER>/Desktop/Boruta_Pipeline_V2_Output/2_Elite_Features_Data"
    OUTPUT_DIR = "C:/Users/<USER>/Desktop/Python_Pipeline_Output"
    FISH_TYPE = "鳙鱼" # 指定当前处理的鱼种
    
    TRAIN_DATA_PATH = os.path.join(BASE_DATA_DIR, "elite_features_TRAIN_raw.csv")
    VAL_DATA_PATH = os.path.join(BASE_DATA_DIR, "elite_features_VALIDATION_raw.csv")
    TEST_DATA_PATH = os.path.join(BASE_DATA_DIR, "elite_features_TEST_raw.csv")
    # ------------------

    try:
        train_and_evaluate_models(
            train_path=TRAIN_DATA_PATH,
            val_path=VAL_DATA_PATH,
            test_path=TEST_DATA_PATH,
            output_dir=OUTPUT_DIR,
            fish_type=FISH_TYPE
        )
    except Exception as e:
        print(f"\nFATAL ERROR: 程序执行失败。", file=sys.stderr)
        traceback.print_exc()
        sys.exit(1)



    