
# 预测模型构建与验证：方法学详述

## 1. 总体分析策略

为了从高维度的微生物组数据中识别出稳健且具有高预测性能的生物标志物，并构建一个可靠的诊断模型，我们设计并执行了一个严格的多阶段分析流程。该流程旨在系统性地减少数据维度、筛选关键特征，并最终构建和验证一个可解释性强的分类模型。整个工作流程主要包括三个核心阶段：

1.  **基于Boruta的微生物特征初筛**：此阶段利用Boruta算法对来自不同分类水平（门、纲、目、科、属、种）的微生物丰度数据进行初步筛选。目标是快速识别出与样本分组（例如，野生与养殖）具有潜在关联性的所有候选特征，为后续更精细的筛选奠定基础。

2.  **基于Bootstrap-LGBM-RFE的鲁棒特征精炼**：在初筛之后，我们采用了一种更为严格的特征精炼策略——结合了自助法（Bootstrap）、LightGBM（LGBM）和递归特征消除（RFE）的集成方法。此阶段旨在从初筛的候选特征中，筛选出不仅预测能力强，而且在不同数据子集中表现稳定的最终生物标志物组合。

3.  **基于LDA的诊断模型构建与验证**：在确定最终的生物标志物组合后，我们使用线性判别分析（Linear Discriminant Analysis, LDA）构建最终的分类诊断模型。我们选择LDA是因为其模型的线性可解释性、计算效率以及在生物数据分析中的广泛应用。模型的性能通过严格的内部交叉验证和独立的外部验证集进行了全面评估。

整个流程确保了从数千个原始微生物特征到一小组核心生物标志物的每一步筛选都有充分的统计学和机器学习依据，最终得到的模型兼具高性能和生物学可解释性。

## 2. 阶段一：基于Boruta的微生物特征初筛

此阶段的目的是从原始丰度数据中进行初步降维，识别出与研究目标（如样本来源）显著相关的候选特征。所有操作均在`boruta鲢鱼.py`脚本中实现。

### 2.1. 数据划分与预处理

-   **数据加载**：加载在门（Phylum）、纲（Class）、目（Order）、科（Family）、属（Genus）、种（Species）六个分类水平上的微生物相对丰度表。
-   **分层抽样**：为了保证训练和验证的可靠性，我们将每个分类水平的数据集按照80:20的比例，采用分层随机抽样（Stratified Sampling）的方式划分为训练集和验证集。分层确保了在训练集和验证集中，不同组别（如野生/养z养殖）的样本比例保持一致，避免了因样本不均衡带来的偏见。

### 2.2. 基于训练集的特征质量过滤

为了确保特征的质量并减少后续计算的噪声，我们**仅在训练集上**实施了一系列严格的过滤步骤。验证集在此阶段保持不变，以模拟真实的未知数据。

1.  **流行度过滤 (Prevalence Filtering)**：移除在训练集样本中出现率低于1%的低流行度特征。
2.  **丰度过滤 (Abundance Filtering)**：移除在训练集样本中平均相对丰度低于0.0001的极低丰度特征。
3.  **近零方差过滤 (Near-Zero Variance Filtering)**：采用模拟R `caret`包中`nearZeroVar`功能的逻辑，移除方差极小或为零的特征。这有助于排除那些在样本间几乎没有变异、信息量极低的特征。
4.  **PCR友好型过滤**：为确保筛选出的特征具有后续湿实验（如qPCR）验证的潜力，我们移除了分类学命名中包含"unclassified"、"uncultured"、"unknown"或数字的特征。

### 2.3. Boruta特征选择

在经过质量过滤的训练集上，我们应用**Boruta算法**进行特征选择。Boruta是一种基于随机森林（Random Forest）的包裹式（Wrapper）特征选择方法，它通过与“影子特征”（将原始特征随机打乱后形成的副本）的重要性进行比较，来判断一个特征是否真正具有预测能力。

-   **核心算法**：我们使用`BorutaPy`库，其核心分类器为`RandomForestClassifier`。随机森林的超参数根据已发表的文献进行了优化（例如，`n_estimators=500`），以提高选择的稳定性。
-   **特征判定**：Boruta将特征分为三类：“Confirmed”（确认重要）、“Tentative”（待定）和“Rejected”（拒绝）。我们仅保留被判定为“Confirmed”的特征进入下一步。

### 2.4. 双重排序法确定候选特征

为了进一步聚焦于最关键的特征，我们对Boruta确认的特征实施了“双重排序法”：

1.  **显著性检验**：对所有“Confirmed”特征，我们计算其在训练集不同组别间的差异显著性（双样本t检验），并使用Benjamini-Hochberg方法对p值进行多重检验校正（FDR, False Discovery Rate）。只有校正后p值（`p_adj`）小于0.05的特征被认为是差异显著的。
2.  **重要性排序**：我们将这些差异显著的特征，按照它们在Boruta随机森林模型中的重要性得分（Importance Score）进行降序排列。
3.  **最终筛选**：在每个分类水平上，我们选取排名最高的**前10个**特征作为该水平的候选生物标志物。

### 2.5. 整合候选特征

最后，我们将从六个分类水平筛选出的所有候选生物标志物合并，创建一个整合的特征集。该整合数据集（包括对应的训练集和验证集）将被用于下一阶段的鲁棒特征精炼。

## 3. 阶段二：基于Bootstrap-LGBM-RFE的鲁棒特征精炼

在获得候选特征集后，此阶段的目标是进一步筛选，得到一个数量更少、预测能力更强且更稳定的最终生物标志物组合。此流程在`ref.py`脚本中定义，其核心是`BootstrapLGBMRFE`方法。

### 3.1. 相关性过滤

在进入复杂的筛选流程之前，我们首先对整合的候选特征集进行**相关性分析**。我们计算所有特征间的皮尔逊（Pearson）相关系数，并移除相关性过高（例如，`|r| > 0.9`）的特征对中的一个，以减少多重共线性对后续模型的影响。

### 3.2. Bootstrap-LGBM-RFE流程

该方法结合了自助法（Bootstrap）、LightGBM模型和递归特征消除（RFE）的优点，以评估特征的稳定性和重要性。

1.  **Bootstrap采样**：我们对训练集进行多次（例如，`n=100`次）有放回的Bootstrap采样，生成多个不同的数据子集。这模拟了从同一总体中多次抽样的情况，用于评估特征选择过程的稳定性。
2.  **递归特征消除 (RFE)**：在**每一个**Bootstrap样本上，我们都执行一次完整的RFE流程：
    -   **模型训练**：使用**LightGBM (LGBM)** 作为RFE的基础模型。LGBM是一种梯度提升决策树模型，以其高效率和高性能而著称。
    -   **特征排序**：在当前特征集上训练LGBM模型，并根据特征重要性（如Gain或Split aacount）对所有特征进行排序。
    -   **特征消除**：移除重要性最低的特征。
    -   **迭代**：重复上述训练和消除过程，直到剩余预设数量的特征。通过此过程，我们得到了在该Bootstrap样本上的一套特征重要性排序。
3.  **结果聚合与最终选择**：
    -   在完成所有Bootstrap循环后，我们统计每个特征在所有RFE排序中的排名或入选频率。
    -   我们选择在所有Bootstrap样本中**平均排名最靠前**、**入选频率最高**的N个特征（例如，N=6）作为最终的生物标志物组合。

通过这种方式，只有那些不仅自身具有强预测能力，而且在数据发生微小变动时仍能被稳定选中的特征，才会被纳入最终的标志物组合，极大地增强了结果的鲁棒性。

## 4. 阶段三：诊断模型的构建与验证

在确定了最终的生物标志物组合后，我们进入模型的构建、训练和全面验证阶段。此流程在`LDA鲢鱼训练.py`脚本中实现。

### 4.1. 数据转换

考虑到微生物组数据的**成分性**（Compositional）特性，即各分类单元的丰度是相对而非绝对的，我们在建模前对数据进行了**中心化对数比转换 (Centered Log-Ratio, CLR)**。CLR转换通过将每个特征值除以该样本中所有特征值的几何平均数，然后取对数，来打破数据的非独立性约束，使其更符合标准统计方法（如LDA）的假设。零值处理则采用基于训练集信息计算的伪计数值进行替换，并将该参数应用于验证集。

### 4.2. LDA模型训练

我们选用**带收缩的线性判别分析 (LDA with Shrinkage)** 作为最终的分类模型。

-   **模型选择理由**：LDA是一个经典且强大的线性分类器，它旨在找到一个能最大化类间距离、最小化类内距离的特征线性组合。其结果具有很强的可解释性，可以直接生成一个判别函数。
-   **收缩 (Shrinkage)**：我们使用`lsqr`求解器并设置`shrinkage='auto'`，采用Ledoit-Wolf引理来自动确定最佳的收缩强度。收缩是一种正则化技术，在高维（特征数接近或超过样本数）或存在多重共线性的情况下尤其有效，可以提高模型的泛化能力，防止过拟合。

### 4.3. 模型性能评估

模型的性能通过内部验证和外部验证两种方式进行了严格评估。

1.  **内部验证**：在训练集上，我们使用10折交叉验证（10-fold Cross-Validation）来评估模型的性能，并获取了包括准确率（Accuracy）、精确率（Precision）、召回率（Recall）、F1分数（F1-Score）、马修斯相关系数（MCC）和ROC曲线下面积（AUC）在内的多项指标的均值和标准差。
2.  **最佳阈值确定**：基于训练集的交叉验证结果，我们绘制了受试者工作特征（ROC）曲线，并采用**约登指数 (Youden's Index)** (`J = Sensitivity + Specificity - 1`) 最大的点作为最佳分类阈值。
3.  **外部验证**：使用在整个分析过程中保持独立的**验证集**，我们对训练好的最终模型（及确定的最佳阈值）进行了性能测试。我们报告了其在验证集上的所有性能指标，以评估模型的真实泛化能力。

### 4.4. 结果可视化与报告

为全面展示模型性能和特征贡献，我们生成了一系列符合出版物标准的可视化图表，包括但不限于：
-   训练集和验证集的**混淆矩阵**。
-   各项性能指标的**柱状图对比**。
-   单特征与组合特征的**ROC曲线对比图**。
-   展示各生物标志物贡献度的**特征重要性图**。
-   将多维数据投影到一维判别轴上的**1D投影分类结果图**。
-   最终标志物组合的**相关性热图**。

同时，我们生成了详细的分析报告，包括判别分析方程、类别趋势分析、性能指标详情和预测结果等，以确保研究的完全透明和可重复性。

## 5. 统计分析与软件

所有数据分析和可视化均使用Python语言（版本3.8+）完成。特征选择和模型构建主要依赖于`scikit-learn`、`boruta`、`lightgbm`等软件包。数据处理和操作使用`pandas`和`numpy`。所有图表均使用`matplotlib`和`seaborn`库绘制，并遵循了出版级的美学标准。所有统计检验的显著性水准（α）均设置为0.05。
