# feature_selection_boruta.R
# 使用Boruta算法进行微生物标志物筛选和可视化 

# ==============================================================================
# --- 0. 加载与初始化 ---
# ==============================================================================

# 加载必要的包
library(Boruta)        # Boruta特征选择
library(ggplot2)       # 可视化
library(dplyr)         # 数据处理
library(tidyr)         # 数据整形
library(ggpubr)        # 添加p值到图形
library(effectsize)
library(patchwork)     # 用于组合图形
library(ggrepel)       # 用于火山图标签
library(caTools)       # 用于数据划分
library(caret)         # 用于创建交叉验证折叠


# ==============================================================================
# --- 核心函数定义 (基于2024年最佳实践) ---
# ==============================================================================

# 函数：从完整的分类学名称中提取最后一级分类
get_short_feature_name <- function(full_name) {
  if (is.null(full_name) || full_name == "") return("")
  parts <- strsplit(full_name, ";")[[1]]
  if (length(parts) == 1) return(full_name) # 如果不含分号，返回原名
  return(parts[length(parts)])
}

# 函数：增强的特征质量过滤 (基于文献最佳实践)
enhanced_feature_quality_filter <- function(data, group_col,
                                           prevalence_threshold = 0.01,
                                           abundance_threshold = 0.0001,
                                           variance_threshold = 0.01) {

  cat("开始增强特征质量过滤 (不包含相关性过滤)...\n")

  # 获取特征列 (排除样本ID和分组列)
  feature_cols <- setdiff(names(data), c("SampleID", group_col))
  feature_data <- data[, feature_cols, drop = FALSE]

  original_features <- length(feature_cols)
  cat("原始特征数:", original_features, "\n")

  # 1. 流行度过滤 - 在至少1%样本中出现
  prevalence_filter <- apply(feature_data, 2, function(x) {
    sum(x > 0) / length(x) >= prevalence_threshold
  })

  features_after_prevalence <- sum(prevalence_filter)
  cat("流行度过滤后特征数:", features_after_prevalence,
      "(移除", original_features - features_after_prevalence, "个)\n")

  # 2. 极低丰度过滤
  abundance_filter <- apply(feature_data, 2, function(x) {
    mean(x) >= abundance_threshold
  })

  # 3. 方差过滤 
  if(require(caret, quietly = TRUE)) {
    nzv_indices <- nearZeroVar(feature_data, saveMetrics = FALSE)
    variance_filter <- rep(TRUE, length(feature_cols))
    if(length(nzv_indices) > 0) {
      variance_filter[nzv_indices] <- FALSE
      cat("方差过滤移除了", length(nzv_indices), "个特征\n")
    } else {
      cat("方差过滤未移除任何特征\n")
    }
  } else {
    variance_filter <- rep(TRUE, length(feature_cols))
    cat("警告: caret包未加载，跳过方差过滤\n")
  }

  # 4. PCR友好过滤 - 移除未分类、未培养、包含数字的特征
  pcr_friendly_filter <- !grepl("unclassified|uncultured|unknown|[0-9]",
                                feature_cols, ignore.case = TRUE)

  # 最终过滤条件 
  final_filter <- prevalence_filter & abundance_filter & variance_filter & pcr_friendly_filter
  features_after_all <- sum(final_filter)
  cat("所有过滤后特征数:", features_after_all,
      "(总过滤效率:", round((1 - features_after_all/original_features) * 100, 2), "%)\n")
  cat("注意: 相关性过滤将在Python LGBM-RFE阶段进行\n")

  # 应用过滤
  filtered_features <- feature_cols[final_filter]
  filtered_data <- data[, c("SampleID", group_col, filtered_features), drop = FALSE]

  return(list(
    data = filtered_data,
    features = filtered_features,
    filter_stats = list(
      original = original_features,
      after_prevalence = features_after_prevalence,
      after_abundance = sum(prevalence_filter & abundance_filter),
      after_variance = sum(prevalence_filter & abundance_filter & variance_filter),
      after_pcr = features_after_all,
      final = length(filtered_features)
    )
  ))
}

# 函数：Bootstrap稳定性评估 (基于文献推荐)
bootstrap_stability_assessment <- function(data, group_col, n_bootstrap = 50,
                                         boruta_params = list()) {

  cat("开始Bootstrap稳定性评估 (", n_bootstrap, "次采样)...\n")

  selected_features_list <- list()

  for(i in 1:n_bootstrap) {
    if(i %% 10 == 0) cat("Bootstrap进度:", i, "/", n_bootstrap, "\n")

    # Bootstrap采样
    boot_indices <- sample(nrow(data), replace = TRUE)
    boot_data <- data[boot_indices, ]

    # 运行Boruta
    tryCatch({
      boruta_result <- Boruta(
        Group ~ .,
        data = boot_data,
        maxRuns = boruta_params$maxRuns %||% 100,
        pValue = boruta_params$pValue %||% 0.01,
        mcAdj = boruta_params$mcAdj %||% TRUE,
        doTrace = 0
      )

      selected_features_list[[i]] <- getSelectedAttributes(boruta_result, withTentative = FALSE)
    }, error = function(e) {
      cat("Bootstrap", i, "失败:", e$message, "\n")
      selected_features_list[[i]] <- character(0)
    })
  }

  # 计算特征选择频率
  all_features <- unique(unlist(selected_features_list))
  feature_frequency <- sapply(all_features, function(feature) {
    sum(sapply(selected_features_list, function(x) feature %in% x)) / n_bootstrap
  })

  # 按频率排序
  feature_frequency <- sort(feature_frequency, decreasing = TRUE)

  cat("Bootstrap稳定性评估完成\n")
  cat("特征选择频率统计:\n")
  print(summary(feature_frequency))

  return(list(
    feature_frequency = feature_frequency,
    selected_features_list = selected_features_list,
    stable_features = names(feature_frequency)[feature_frequency >= 0.5]  # 50%以上频率
  ))
}

# ==============================================================================
# --- 1. 全局参数设置 (V3) ---
# ==============================================================================

# --- 1.1 输入文件配置 ---
input_files <- list(
  'phylum' = 'C:\\Users\\<USER>\\Desktop\\特征提取\\L门.txt',
  'class' = 'C:\\Users\\<USER>\\Desktop\\特征提取\\L纲.txt',
  'order' = 'C:\\Users\\<USER>\\Desktop\\特征提取\\L目.txt',
  'family' = 'C:\\Users\\<USER>\\Desktop\\特征提取\\L科.txt',
  'genus' = 'C:\\Users\\<USER>\\Desktop\\特征提取\\L属.txt',
  'species' = 'C:\\Users\\<USER>\\Desktop\\特征提取\\L种.txt'
)

# --- 1.2 输出目录配置 ---
base_output_dir <- "C:/Users/<USER>/Desktop/Boruta_Pipeline_V2_Output" 
transformed_data_dir <- file.path(base_output_dir, "1_Transformed_Per_Level")
elite_features_dir <- file.path(base_output_dir, "2_Elite_Features_Data")
per_level_analysis_dir <- file.path(base_output_dir, "3_Analysis_Per_Level")
final_plots_dir <- file.path(base_output_dir, "4_Final_Combined_Plots")

# --- 1.3 数据划分参数 (基于2024年最佳实践) ---
sample_id_col <- "SampleID"     # 样本ID列名
group_col <- "Group"            # 分组列名
train_ratio <- 0.8              # 训练集比例 (80/20划分)
prevalence_threshold <- 0.01    # 流行度阈值 (1%)
abundance_threshold <- 0.0001   # 极低丰度阈值
set.seed(42)                    # 固定随机种子确保可重复性

# --- 1.4 Boruta参数配置 (基于Mayo Clinic 2024最佳实践) ---
boruta_max_runs <- 500          # 增加运行次数提高稳定性
boruta_p_value <- 0.01          # 更严格的p值阈值
boruta_mc_adj <- TRUE           # 启用多重比较校正 (关键!)
boruta_do_trace <- 1            # 显示进度
bootstrap_n <- 500            # Bootstrap稳定性评估次数 (基于文献推荐)
use_bootstrap_stability <- TRUE # 是否使用Bootstrap稳定性评估


# --- 1.5 特征质量过滤参数 ---
variance_threshold <- 0.01      # 方差过滤阈值
# 注意: 相关性过滤已移至Python LGBM-RFE阶段，避免重复处理

# --- 1.5 初始化结果收集器 ---
all_levels_top_features_df <- data.frame()
all_levels_top_features_stats_df <- data.frame()
all_levels_volcano_data_df <- data.frame()
all_name_correspondence_df <- data.frame()
elite_features_all_levels <- character()

# --- 1.6 创建输出文件夹 ---
dir.create(base_output_dir, showWarnings = FALSE, recursive = TRUE)
dir.create(transformed_data_dir, showWarnings = FALSE, recursive = TRUE)
dir.create(elite_features_dir, showWarnings = FALSE, recursive = TRUE)
dir.create(per_level_analysis_dir, showWarnings = FALSE, recursive = TRUE)
dir.create(final_plots_dir, showWarnings = FALSE, recursive = TRUE)

# ==============================================================================
# --- 2. 全局数据划分 (V3) ---
# ==============================================================================
cat("\n--- 步骤2: 全局数据划分 (训练集/验证集) ---\n")

# 使用第一个文件来获取完整的样本ID和分组信息
base_info_file <- read.table(input_files[[1]], header = TRUE, sep = "\t", check.names = FALSE)
if(!group_col %in% colnames(base_info_file)) {
  stop(paste("错误: 分组列 '", group_col, "' 未在基础信息文件中找到。", sep=""))
}
if(!sample_id_col %in% colnames(base_info_file)) {
  stop(paste("错误: 样本ID列 '", sample_id_col, "' 未在基础信息文件中找到。", sep=""))
}

sample_info <- base_info_file[, c(sample_id_col, group_col)]
sample_info[[group_col]] <- as.factor(sample_info[[group_col]])

# 创建分层抽样的训练集和验证集索引
set.seed(123)
train_indices <- createDataPartition(sample_info[[group_col]], p = train_ratio, list = FALSE, times = 1)
train_ids <- sample_info[[sample_id_col]][train_indices]
val_ids <- sample_info[[sample_id_col]][-train_indices]

cat("全局数据划分完成:\n")
cat(" - 训练集样本数:", length(train_ids), "\n")
cat(" - 验证集样本数:", length(val_ids), "\n")
cat("训练集分组分布:\n"); print(table(sample_info[sample_info[[sample_id_col]] %in% train_ids, group_col]))
cat("验证集分组分布:\n"); print(table(sample_info[sample_info[[sample_id_col]] %in% val_ids, group_col]))

# 基于2024年文献最佳实践: Boruta直接在训练集上运行，无需交叉验证
cat("\nBoruta将直接在训练集上运行 (基于文献最佳实践):\n")
cat(" - 避免数据泄漏: ✅\n")
cat(" - 计算效率优化: ✅\n")
cat(" - 后续LGBM-RFE进行交叉验证: ✅\n")

# 清理内存
rm(base_info_file)


# ==============================================================================
# --- 3. 主处理循环：按分类水平进行串行特征选择 (V3) ---
# ==============================================================================

# --- 3.0 串行处理 ---
cat("\n--- 步骤3: 开始在所有分类水平上以串行模式执行特征选择... ---\n")

results_list <- lapply(names(input_files), function(level_name) {
  
  cat(paste("\n--- [", Sys.time(), "] 开始处理分类水平:", toupper(level_name), "---\n"))

  # --- [内部函数] ---
  get_short_feature_name <- function(full_name) {
    if (is.null(full_name) || full_name == "") return("")
    parts <- strsplit(full_name, ";")[[1]]
    if (length(parts) == 1) return(full_name)
    return(parts[length(parts)])
  }
  
  #  基于最佳实践，Boruta直接在训练集上运行
  
  data_file_path <- input_files[[level_name]]
  level_output_dir <- file.path(per_level_analysis_dir, level_name)
  dir.create(level_output_dir, showWarnings = FALSE, recursive = TRUE)

  # --- 3.1 数据加载与划分 ---
  full_data <- read.table(data_file_path, header = TRUE, sep = "\t", check.names = FALSE, 
                          stringsAsFactors = FALSE, comment.char = "")

  train_data_raw <- full_data[full_data[[sample_id_col]] %in% train_ids, ]
  
  # --- 3.2 特征质量过滤 (在训练集上) ---
  original_features <- setdiff(colnames(train_data_raw), c(sample_id_col, group_col))
  feature_data <- train_data_raw[, original_features, drop = FALSE]
  
  # 1. 流行度、丰度和PCR友好型过滤
  prevalence_filter <- apply(feature_data, 2, function(x) sum(x > 0) / length(x) >= 0.01)
  abundance_filter <- apply(feature_data, 2, function(x) mean(x) >= 0.0001)
  pcr_friendly_filter <- !grepl("unclassified|uncultured|[0-9]", original_features, ignore.case = TRUE)
  
  # 2. 方差过滤
  # 使用caret包的nearZeroVar来移除方差极低的特征
  # saveMetrics = TRUE 
  nzv_indices <- nearZeroVar(feature_data, saveMetrics = FALSE)
  variance_filter <- rep(TRUE, length(original_features))
  if(length(nzv_indices) > 0) {
    variance_filter[nzv_indices] <- FALSE
    cat("  - 方差过滤移除了", length(nzv_indices), "个特征。\n")
  } else {
    cat("  - 方差过滤未移除任何特征。\n")
  }

  final_filter <- prevalence_filter & abundance_filter & pcr_friendly_filter & variance_filter
  features_to_keep <- original_features[final_filter]
  
  if(length(features_to_keep) == 0) {
    return(NULL) # 如果没有特征，直接从此迭代返回NULL
  }
  
  train_data_filtered <- train_data_raw[, c(sample_id_col, group_col, features_to_keep)]
  
  # --- 3.3 Boruta特征选择 (在过滤后的训练集上) ---
  raw_short_names <- sapply(features_to_keep, get_short_feature_name, USE.NAMES = FALSE)
  processed_feature_names <- make.names(raw_short_names, unique = TRUE)
  name_correspondence <- data.frame(原始完整名称 = features_to_keep, 
                                  处理后特征名 = processed_feature_names)
  
  train_data_for_boruta <- train_data_filtered
  colnames(train_data_for_boruta) <- c(sample_id_col, group_col, processed_feature_names)
  
  # --- 3.3 直接在训练集上运行Boruta ---
  cat("  步骤3.3: 在训练集上运行Boruta特征选择...\n")

  # 准备Boruta输入数据
  boruta_input_data <- train_data_for_boruta[, !(names(train_data_for_boruta) %in% c(sample_id_col))]
  boruta_input_data$Group <- as.factor(boruta_input_data$Group)

  cat("  Boruta输入数据维度:", nrow(boruta_input_data), "样本 ×", ncol(boruta_input_data)-1, "特征\n")

  # Boruta调用
  cat("  开始Boruta分析 (maxRuns =", boruta_max_runs, ", pValue =", boruta_p_value, ")...\n")

  boruta_result <- Boruta(
    Group ~ .,
    data = boruta_input_data,
    maxRuns = boruta_max_runs,     # 500次运行提高稳定性
    pValue = boruta_p_value,       # 0.01严格阈值
    mcAdj = boruta_mc_adj,         # 多重比较校正
    doTrace = boruta_do_trace      # 显示进度
  )

  # 获取确认的特征
  confirmed_features <- getSelectedAttributes(boruta_result, withTentative = FALSE)
  tentative_features <- getSelectedAttributes(boruta_result, withTentative = TRUE)

  cat("  Boruta结果:\n")
  cat("    - 确认特征数:", length(confirmed_features), "\n")
  cat("    - 包含待定特征数:", length(tentative_features), "\n")

  # --- 3.4 Bootstrap稳定性评估 ---
  if(use_bootstrap_stability && length(confirmed_features) > 0) {
    cat("  步骤3.4: 进行Bootstrap稳定性评估...\n")

    # 准备Bootstrap参数
    bootstrap_params <- list(
      maxRuns = boruta_max_runs,
      pValue = boruta_p_value,
      mcAdj = boruta_mc_adj
    )

    # 运行Bootstrap稳定性评估
    bootstrap_result <- bootstrap_stability_assessment(
      data = boruta_input_data,
      group_col = "Group",
      n_bootstrap = bootstrap_n,
      boruta_params = bootstrap_params
    )

    # 使用Bootstrap稳定特征 (频率>=50%)
    stable_features_processed <- bootstrap_result$stable_features

    cat("  Bootstrap稳定性评估完成:\n")
    cat("    - 稳定特征数 (频率>=50%):", length(stable_features_processed), "\n")
    cat("    - 平均选择频率:", round(mean(bootstrap_result$feature_frequency), 3), "\n")

  } else {
    # 直接使用Boruta确认的特征
    stable_features_processed <- confirmed_features
    cat("  使用Boruta确认特征 (未进行Bootstrap评估)\n")
  }
  
  # --- 3.5 收集结果 ---
  iter_elite_original_names <- name_correspondence$原始完整名称[name_correspondence$处理后特征名 %in% stable_features_processed]
  
  return(list(
    level_name = level_name,
    elite_names = iter_elite_original_names
  ))
})

# --- 3.6 清理 ---
cat("\n--- 所有分类水平处理完成。---\n")

# --- 3.7 汇总所有任务的结果 ---
cat("\n--- 汇总所有级别的分析结果... ---\n")
for (res in results_list) {
  if (!is.null(res)) {
    cat("  - 处理级别:", res$level_name, "- 发现", length(res$elite_names), "个精英特征\n")
    if (length(res$elite_names) > 0) {
      elite_features_all_levels <- c(elite_features_all_levels, res$elite_names)
  }
  }
}
cat("所有结果汇总完毕。\n")

# ==============================================================================
# --- 4. 最终精英特征数据整理与保存 (V3) ---
# ==============================================================================
cat("\n\n====================================================================\n")
cat("--- 步骤4: 整理并保存所有水平的精英特征数据 ---\n")
cat("====================================================================\n")

unique_elite_features <- unique(elite_features_all_levels)
cat("所有水平共选出", length(unique_elite_features), "个独特的精英特征。\n")

if (length(unique_elite_features) > 0) {
    # --- 4.1 合并所有原始数据以提取精英特征 ---
    cat("合并所有输入文件以提取精英特征的原始丰度数据...\n")
    
    base_data <- read.table(input_files[[1]], header = TRUE, sep = "\t", check.names = FALSE)[, c(sample_id_col, group_col)]
    
    list_of_feature_data <- lapply(input_files, function(file_path) {
        temp_data <- read.table(file_path, header = TRUE, sep = "\t", check.names = FALSE)
        features_in_file <- intersect(unique_elite_features, colnames(temp_data))
        if (length(features_in_file) > 0) {
            return(temp_data[, c(sample_id_col, features_in_file), drop = FALSE])
        }
        return(NULL)
    })
    
        list_of_feature_data <- Filter(Negate(is.null), list_of_feature_data)
        list_to_merge <- append(list(base_data), list_of_feature_data)
        
        final_elite_data_raw <- Reduce(function(df1, df2) merge(df1, df2, by = sample_id_col, all = TRUE), list_to_merge)
    final_elite_data_raw[is.na(final_elite_data_raw)] <- 0

    # --- 4.2 按全局ID划分精英特征数据并保存 ---
    cat("按全局划分ID保存精英特征的原始丰度数据...\n")
    elite_train_raw <- final_elite_data_raw[final_elite_data_raw[[sample_id_col]] %in% train_ids, ]
    elite_val_raw <- final_elite_data_raw[final_elite_data_raw[[sample_id_col]] %in% val_ids, ]
        
        write.csv(elite_train_raw, file.path(elite_features_dir, "elite_features_TRAIN_raw.csv"), row.names = FALSE)
        write.csv(elite_val_raw, file.path(elite_features_dir, "elite_features_VALIDATION_raw.csv"), row.names = FALSE)
        cat("精英特征的原始丰度数据已保存至:", elite_features_dir, "\n")

        # --- 4.4 保存最终精英特征列表 (为下游Python脚本准备) ---
        cat("保存最终精英特征列表 (用于LGBM-RFE)...\n")
        final_elite_features_list_df <- data.frame(feature = unique_elite_features)
        output_feature_list_path <- file.path(elite_features_dir, "boruta_selected_features.csv")
        write.csv(final_elite_features_list_df, output_feature_list_path, row.names = FALSE, quote = TRUE)
        cat("最终精英特征列表已保存至:", output_feature_list_path, "\n")
} else {
    cat("警告: 未能收集到任何精英特征，无法生成最终的组合数据文件。\n")
}

# ==============================================================================
# --- 5. 结束 ---
# ==============================================================================
cat("\n\n--- 整个自动化流水线执行完毕! ---\n")
cat("所有输出文件已保存到主目录:", base_output_dir, "\n")