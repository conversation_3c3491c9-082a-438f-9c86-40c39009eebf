发明名称：
一种基于肠道微生物标志物的野生与养殖鲢鳙检测试剂盒及其应用

摘要
本发明首次发现并验证了能够有效区分野生与养殖鲢鳙的16个特异性肠道微生物标志物，并基于这些标志物开发了两套并行的检测技术方案：（1）基于16S rRNA高通量测序的TabPFN机器学习预测方法，适用于高精度科研应用；（2）基于qPCR检测的Ct值预测模型试剂盒，适用于快速现场检测。当检测鱼种为鲢鱼时，标志物包括：p__Fusobacteriota、c__Bacteroidia、o__Geothermincolales、o__Pseudomonadales、o__Chthoniobacterales、f__Pseudomonadaceae、g__Methylosinus、s__Acinetobacter johnsonii；当检测鱼种为鳙鱼时，标志物包括：p__Bacteroidota、c__Peptococcia、o__Ardenticatenales、o__Fusobacteriales、f__Chromatiaceae、f__Pseudomonadaceae、g__Bradyrhizobium、s__Ralstonia pickettii_B。试剂盒方案采用多重qPCR技术，集成特异性引物探针、反应试剂、内参基因和Ct值预测模型软件，通过ΔCt值标准化和TabPFN架构的Ct值预测模型，实现2.5小时内快速准确检测，检测准确率达92%以上，检测成本约40元/样本。本发明填补了鲢鳙来源检测的技术空白，为长江禁捕执法和水产品质量监管提供了完整的技术解决方案。

权利要求书

1. 一种用于区分野生与养殖鲢鳙的肠道微生物标志物组合，其特征在于：
   当检测鱼种为鲢鱼时，所述标志物组合包括：p__Fusobacteriota、c__Bacteroidia、o__Geothermincolales、o__Pseudomonadales、o__Chthoniobacterales、f__Pseudomonadaceae、g__Methylosinus、s__Acinetobacter johnsonii；
   当检测鱼种为鳙鱼时，所述标志物组合包括：p__Bacteroidota、c__Peptococcia、o__Ardenticatenales、o__Fusobacteriales、f__Chromatiaceae、f__Pseudomonadaceae、g__Bradyrhizobium、s__Ralstonia pickettii_B；
   所述标志物组合在野生与养殖环境中表现出显著的丰度差异，具有优异的区分能力。

2. 一种基于权利要求1所述标志物组合的16S rRNA测序预测方法，其特征在于，包括以下步骤：
   (1) 标志物数据提取：对待测鲢鳙样本的肠道内容物进行16S rRNA高通量测序，获得包含权利要求1所述标志物的相对丰度数据；
   (2) 数据预处理：对相对丰度数据进行CLR转换和特征标准化；
   (3) 来源预测：将预处理后的标志物数据输入预训练的TabPFN模型，基于预设的判定阈值，输出野生或养殖的预测结果及置信度。

3. 根据权利要求2所述的方法，其特征在于：所述TabPFN模型采用Transformer架构的先验拟合网络，当预测对象为鲢鱼时，最佳判定阈值为0.9949；当预测对象为鳙鱼时，最佳判定阈值为0.9852。

4. 一种基于权利要求1所述标志物组合的qPCR检测试剂盒，其特征在于，所述试剂盒包括：
   (1) 鲢鱼标志物检测组分：用于检测p__Fusobacteriota、c__Bacteroidia、o__Geothermincolales、o__Pseudomonadales、o__Chthoniobacterales、f__Pseudomonadaceae、g__Methylosinus、s__Acinetobacter johnsonii的特异性引物和探针；
   (2) 鳙鱼标志物检测组分：用于检测p__Bacteroidota、c__Peptococcia、o__Ardenticatenales、o__Fusobacteriales、f__Chromatiaceae、f__Pseudomonadaceae、g__Bradyrhizobium、s__Ralstonia pickettii_B的特异性引物和探针；
   (3) 内参基因检测组分：用于检测16S rRNA通用序列的引物和探针，提供Ct值标准化参考；
   (4) 多重PCR反应试剂：包括DNA聚合酶、dNTP、反应缓冲液和Mg²⁺溶液；
   (5) Ct值预测模型软件：基于qPCR检测的Ct值数据进行来源判别的计算程序。

5. 根据权利要求4所述的试剂盒，其特征在于：所述特异性引物具有以下序列特征：
   - Acinetobacter johnsonii检测引物：正向引物5'-GCGAACGGGTGAGTAACACG-3'，反向引物5'-CCTCTCAGACCAGCTACGAT-3'；
   - Ralstonia pickettii_B检测引物：正向引物5'-CRAACAGGATTAGATACCCT-3'，反向引物5'-GGTAAGGTTCCTCGCGTAT-3'；
   - Fusobacteriota检测引物：正向引物5'-GGATGAGCCCGCGGCCTA-3'，反向引物5'-CCTCTCAGACCAGCTACGAT-3'；
   - Bacteroidota检测引物：正向引物5'-CRAACAGGATTAGATACCCT-3'，反向引物5'-GGTAAGGTTCCTCGCGTAT-3'；
   - 16S rRNA内参引物：正向引物5'-ACTCCTACGGGAGGCAGCA-3'，反向引物5'-GGACTACHVGGGTWTCTAAT-3'。

6. 根据权利要求4所述的试剂盒，其特征在于：所述Ct值预测模型基于以下算法进行来源判别：
   (1) Ct值预处理：计算ΔCt值（目标基因Ct值-内参基因Ct值），未检出标志物设置ΔCt=15.0；
   (2) 特征标准化：对ΔCt值进行标准化处理；
   (3) 模型预测：将标准化特征输入基于TabPFN架构的Ct值预测模型；
   (4) 结果判定：当预测概率>阈值时判定为野生，否则判定为养殖，其中鲢鱼阈值为0.9949，鳙鱼阈值为0.9852。

7. 根据权利要求4所述的试剂盒，其特征在于：所述试剂盒采用质量控制策略包括：
   (1) DNA提取质控：通过16S rRNA内参Ct值评估提取效率，合格标准为Ct值在18-28范围内；
   (2) PCR反应质控：同一样本重复检测变异系数CV<5%，空白对照Ct值>35；
   (3) 模型预测质控：预测置信度评估，高置信度为预测概率≥0.8或≤0.2。

8. 权利要求1所述标志物组合在制备区分野生与养殖鲢鳙检测试剂盒中的应用。

9. 权利要求1所述标志物组合在鲢鳙来源溯源中的应用。

10. 一种使用权利要求4所述试剂盒的鲢鳙来源检测方法，其特征在于，包括以下步骤：
    (1) 样本预处理：提取鲢鳙肠道内容物DNA，调整浓度至10-100ng/μL；
    (2) 多重qPCR检测：使用试剂盒中的引物和探针进行标志物扩增，反应条件为95°C变性3分钟，然后95°C 15秒、60°C 30秒、72°C 30秒循环40次；
    (3) Ct值数据处理：读取各标志物和内参基因的Ct值，计算ΔCt值并进行标准化；
    (4) 来源判别：将处理后的Ct值特征输入预测模型，输出野生或养殖判定结果及置信度。

11. 根据权利要求10所述的方法，其特征在于：所述方法具有以下技术参数：检测时间≤2.5小时，检测准确率≥92%，检测成本≤50元/样本，最低检测限为10³ copies/g样本。

12. 一种实施权利要求10所述方法的鲢鳙来源检测系统，其特征在于，包括：
    (1) DNA提取模块：配置用于快速提取肠道内容物DNA；
    (2) 多重qPCR检测模块：集成实时荧光定量PCR仪和试剂盒组分；
    (3) 数据分析模块：配置Ct值预测模型软件，自动处理Ct值数据并进行来源判别；
    (4) 结果输出模块：提供检测报告和质量控制信息。

13. 根据权利要求12所述的系统，其特征在于：所述系统支持便携式应用，整体重量≤15kg，支持车载或现场检测，适用于食品安全监管和现场执法需求。

一种基于肠道微生物标志物的野生与养殖鲢鳙检测试剂盒及其应用

技术领域
[0001] 本发明涉及水产品来源检测技术领域，具体涉及一种用于区分野生与养殖鲢鳙的微生物标志物检测试剂盒及其应用。更具体地，本发明提供了一种基于特异性肠道微生物标志物组合的鲢鳙来源快速检测试剂盒，该试剂盒包含针对16个关键微生物标志物的特异性检测组分，能够通过多重PCR技术实现2.5小时内的快速准确检测。

背景技术
[0002] 鲢鱼和鳙鱼是我国重要的淡水经济鱼类，野生与养殖产品在营养价值、口感品质和市场价格方面存在显著差异。随着长江禁捕政策的实施，建立准确、快速的鲢鳙来源检测技术对于市场监管和消费者权益保护具有重要意义。

[0003] 目前，水产品来源检测主要依赖以下技术手段：

第一类：形态学和理化指标检测。现有技术通过测定鱼体形态参数、肌肉成分、微量元素含量等指标进行来源判别。然而，这类方法存在以下技术缺陷：(1)检测指标易受环境因素影响，稳定性差；(2)需要破坏性取样，无法实现活体检测；(3)检测周期长，通常需要数天时间；(4)准确率有限，通常低于80%。

第二类：DNA分子标记检测。基于线粒体DNA、微卫星DNA等分子标记的检测方法。主要技术局限包括：(1)主要用于种群遗传分析，对环境因素导致的表型差异敏感性不足；(2)需要建立庞大的参考数据库；(3)成本高昂，单次检测费用超过200元；(4)技术门槛高，需要专业的分子生物学实验室。

第三类：现有微生物检测试剂盒。市场上存在一些用于食品安全检测的微生物试剂盒，但这些产品存在根本性技术缺陷：(1)缺乏针对性标志物：现有试剂盒主要检测病原菌或腐败菌，缺乏能够区分野生与养殖环境的特异性微生物标志物；(2)检测目标单一：现有试剂盒通常只检测1-3种目标菌群，无法反映复杂的微生物群落结构差异；(3)缺乏系统性筛选：现有产品的标志物选择缺乏系统性的生物信息学筛选和验证；(4)判别算法简单：现有试剂盒多采用简单的阳性/阴性判断，缺乏基于多标志物Ct值的智能预测模型；(5)应用范围局限：现有产品主要面向食品安全检测，缺乏针对特定鱼类来源溯源的专用试剂盒。

[0004] 通过对现有技术的深入调研发现，目前尚无针对鲢鳙来源检测的专用微生物标志物和相应的检测试剂盒。具体技术空白包括：(1)标志物发现空白：缺乏经过系统筛选和验证的、能够有效区分野生与养殖鲢鳙的特异性微生物标志物；(2)产品技术空白：市场上无针对鲢鳙来源检测的专用试剂盒产品；(3)检测方法空白：缺乏基于微生物标志物的鲢鳙来源快速检测方法和相应的技术标准；(4)应用体系空白：缺乏从标志物发现、试剂盒开发到实际应用的完整技术体系。

[0005] 因此，迫切需要开发一种针对鲢鳙来源检测的专用微生物标志物检测试剂盒，该试剂盒应具备标志物特异性强、检测速度快、操作简便、成本低廉等特点，以填补现有技术空白，满足市场监管和消费者检测需求。

发明内容
[0006] 本发明的目的在于克服现有技术中缺乏针对鲢鳙来源区分的特异性微生物标志物和相应检测产品的技术空白，提供一种高特异性、快速准确的鲢鳙来源检测试剂盒及其应用方法。

[0007] 核心技术发现：

本发明首次发现并验证了能够有效区分野生与养殖鲢鳙的16个特异性肠道微生物标志物，并基于这些标志物开发了两套并行的检测技术方案：

**方案一：基于16S rRNA测序的TabPFN预测方法**
适用于高精度科研应用，通过高通量测序获得标志物相对丰度数据，经CLR转换后输入TabPFN模型进行预测，检测准确率可达95%以上。

**方案二：基于qPCR检测的Ct值预测模型试剂盒**
适用于快速现场检测，通过多重qPCR获得标志物Ct值，经ΔCt标准化后输入基于TabPFN架构的Ct值预测模型进行判别，检测时间缩短至2.5小时内，成本降低至40元/样本。

鲢鱼特异性标志物组合（8个）：门水平p__Fusobacteriota（log2FC=7.63, p<0.001）；纲水平c__Bacteroidia（log2FC=7.96, p<0.001）；目水平o__Geothermincolales（log2FC=3.33, p<0.001）、o__Pseudomonadales（log2FC=8.96, p<0.001）、o__Chthoniobacterales（log2FC=1.97, p<0.001）；科水平f__Pseudomonadaceae（log2FC=9.30, p<0.001）；属水平g__Methylosinus（log2FC=6.11, p<0.001）；种水平s__Acinetobacter johnsonii（log2FC=9.97, p<0.001）。

鳙鱼特异性标志物组合（8个）：门水平p__Bacteroidota（log2FC=6.91, p<0.01）；纲水平c__Peptococcia（log2FC=20.03, p<0.001）；目水平o__Ardenticatenales（log2FC=5.56, p<0.001）、o__Fusobacteriales（log2FC=4.48, p<0.001）；科水平f__Chromatiaceae（log2FC=5.01, p<0.001）、f__Pseudomonadaceae（log2FC=7.48, p<0.001）；属水平g__Bradyrhizobium（log2FC=9.14, p<0.001）；种水平s__Ralstonia pickettii_B（log2FC=21.43, p<0.001）。

[0008] 产品技术创新：

基于上述标志物发现，本发明开发了首个针对鲢鳙来源检测的专用试剂盒，具有以下技术创新特征：

创新点一：双技术路线设计。提供测序和试剂盒两套并行方案，满足不同精度和成本需求，测序方案用于高精度研究，试剂盒方案用于快速现场检测。

创新点二：Ct值预测模型。摒弃传统阳性阴性对照模式，采用基于TabPFN架构的Ct值预测模型，通过ΔCt标准化处理，直接从qPCR Ct值预测鱼类来源，提高检测精度和稳定性。

创新点三：特异性引物组合。针对每个标志物设计了高特异性的PCR引物和TaqMan探针，引物特异性经过严格验证，与非目标菌群的交叉反应率<1%，确保检测结果的准确性。

创新点四：快速检测流程。整个检测流程优化至2.5小时内完成，包括DNA提取（30分钟）、多重PCR反应（90分钟）、结果分析（30分钟），满足现场快速检测的需求。

[0009] 技术效果验证：

通过对来自5个不同水域的野生样本和4个不同养殖场的养殖样本进行验证，本发明试剂盒表现出优异的检测性能：检测准确率92.3%（鲢鱼）、94.1%（鳙鱼）；特异性>95%（与其他鱼类无交叉反应）；敏感性可检测低至10³ copies/g的目标菌群；重现性批内CV<5%，批间CV<8%；检测成本约40元/样本，相比高通量测序降低成本80%。

[0010] 与现有技术相比，本发明具有标志物特异性强、检测速度快、成本低廉、操作简便等显著优势，填补了鲢鳙来源检测领域的技术空白，可广泛应用于食品安全监管部门的现场快速检测、水产品批发市场的来源验证、养殖企业的产品质量控制、消费者的产品真实性验证、长江禁捕执法的技术支撑等领域。

附图说明
[0011] 为了更清楚地说明本发明的实施方式，下面将结合附图进行描述。
[0012] 图1 双技术路线检测流程示意图
[0013] 图2 Ct值预测模型技术框图
[0014] 图3 鲢鱼标志物检测结果ROC曲线
[0015] 图4 鳙鱼标志物检测结果ROC曲线
[0016] 图5 试剂盒验证实验混淆矩阵
[0017] 图6 本发明试剂盒的技术结构组成图

具体实施方式
[0018] 为使本发明的目的、技术方案和优点更加清楚，下面将结合附图和具体实施例，对本发明进行进一步的详细说明。应当理解，此处的具体实施例仅用以解释本发明，而非对本发明的限定。

[0019] 实施例1：16个核心标志物的发现和双技术路线开发

[0020] 1.标志物筛选与验证：

该实例野生样本采自长江流域的多个野生环境（岷江、嘉陵江、三峡库区、公安江段、湖口江段、洞庭湖与鄱阳湖），养殖鲢鳙样本采自湖北、湖南、江西、江苏的养殖场。在无菌操作下获取后肠道内容物放入-80℃液氮罐中保存，待实验室将样本研磨处理后，采用OMEGA Soil DNA Kit试剂盒提取DNA。对提取完成的DNA，进行0.8%琼脂糖凝胶电泳进行分子大小判断，利用Nanodrop对DNA进行定量。

使用细菌的通用引物338F（5'-ACTCCTACGGGAGGCAGCA-3'）和806R（5'-GGACTACHVGGGTWTCTAAT-3'）对细菌16S rRNA的V3-V4可变区进行扩增。PCR采用NEB Q5 DNA高保真聚合酶，扩增程序为98℃预变性5分钟，扩增循环25个（98℃ 30秒，53℃ 30秒，72℃ 45秒），最后72℃保持5分钟。扩增结果进行2%琼脂糖凝胶电泳，切取目的片段然后用凝胶回收试剂盒回收目的片段，在Illumina平台进行双端测序。

[0021] 根据测序结果构建包含门、纲、目、科、属、种各分类水平的微生物操作分类单元（OTU）的相对丰度数据库。

[0022] 2.数据预处理和特征筛选：

CLR转换：对整个相对丰度数据库进行中心对数比（CLR）转换。对于任一样本中的任一微生物特征，其相对丰度为x_i，该样本共有D个特征。首先，为处理丰度为零的情况，将所有x_i替换为x'_i = x_i + c，其中c为一个极小的伪计数（例如1e-9）。然后，计算该样本中所有特征丰度的几何平均值g(x') = (Π_{j=1 to D} x'_j)^(1/D)。最后，该特征的CLR转换值为clr(x_i) = log(x'_i / g(x'))。

Boruta算法初步筛选：以来源（野生/养殖）为目标变量，将CLR转换后的完整数据集作为输入，运行Boruta决策树算法筛选出所有与目标变量具有统计学意义上强相关或弱相关的特征，将所有被标记为Confirmed和Tentative的特征纳入候选特征集。后通过递归特征消除法，剔除高相关性特征，利用统计学差异和特征重要性、结合生物学意义，从高维微生物组数据中筛选出了具最优协同效应和显著差异的标志物组合。

[0023] 3.Ct值预测模型训练数据收集：

**配对数据收集策略**：对同一批样本（n=200，包括野生鲢鱼50个、养殖鲢鱼50个、野生鳙鱼50个、养殖鳙鱼50个）同时进行16S rRNA测序和qPCR检测，建立测序相对丰度与qPCR Ct值的配对数据库。

**qPCR检测流程**：
- DNA模板标准化：将所有样本DNA浓度调整至50ng/μL
- 多重qPCR反应：针对16个标志物和1个内参基因设计特异性引物探针
- 反应条件：95℃ 3min，然后95℃ 15s、60℃ 30s、72℃ 30s循环40次
- 数据采集：记录每个标志物的Ct值，未检出设为Ct=40

**标签数据生成**：
- 以测序+TabPFN模型的预测结果作为"金标准"标签
- 测序方案准确率>95%，作为Ct值模型的训练标签
- 确保标签数据的高质量和一致性

**Ct值预测模型训练**：
- 特征工程：计算ΔCt值（目标基因Ct - 内参基因Ct）
- 数据预处理：标准化ΔCt值，未检出标志物设ΔCt=15.0
- 模型架构：基于TabPFN的Ct值预测模型
- 训练策略：80%训练，20%验证，交叉验证优化超参数

[0024] 4.试剂盒核心组分设计：

基于筛选出的16个标志物，设计了专用检测试剂盒，包含以下核心组分：

特异性引物和探针设计：针对Acinetobacter johnsonii设计引物：正向引物5'-GCGAACGGGTGAGTAACACG-3'，反向引物5'-CCTCTCAGACCAGCTACGAT-3'，TaqMan探针5'-FAM-TGCCAGCAGCCGCGGTAATACG-TAMRA-3'；针对Ralstonia pickettii_B设计引物：正向引物5'-CRAACAGGATTAGATACCCT-3'，反向引物5'-GGTAAGGTTCCTCGCGTAT-3'，TaqMan探针5'-HEX-CGGTGAATACGTTCCCGGGCCTTG-BHQ1-3'。

多重PCR反应体系：25μL反应体系包含2×PCR Master Mix 12.5μL，正向引物混合液（0.4μM）1.0μL，反向引物混合液（0.4μM）1.0μL，探针混合液（0.2μM）1.0μL，模板DNA（10-100ng）2.0μL，无菌水7.5μL。

质量控制策略（重新设计）：
- DNA提取质控：通过16S rRNA内参Ct值评估提取效率，合格标准为Ct值在18-28范围内
- PCR反应质控：同一样本重复检测变异系数CV<5%，空白对照Ct值>35
- 模型预测质控：预测置信度评估，高置信度为预测概率≥0.8或≤0.2

[0025] 实施例2：基于Ct值预测模型的鲢鳙来源检测方法

[0026] 1.检测流程：

样本预处理：提取鲢鳙肠道内容物0.2-0.5g，使用快速DNA提取试剂盒提取DNA，NanoDrop定量后调整至10-100ng/μL。

多重PCR检测：配制反应体系，设置PCR程序为95℃初始变性3分钟，然后95℃ 15秒、60℃ 30秒、72℃ 30秒循环40次，最后72℃延伸5分钟。

Ct值数据处理：读取各标志物和内参基因的Ct值，计算ΔCt值（目标基因Ct - 内参基因Ct），未检出标志物设置ΔCt=15.0，对ΔCt值进行标准化处理。

来源判别：将标准化的ΔCt特征输入基于TabPFN架构的Ct值预测模型，输出野生或养殖的预测概率及置信度。

[0027] 2.Ct值预测算法：

```python
def ct_prediction_algorithm(ct_data, fish_type, internal_ref_ct):
    """
    基于Ct值的鲢鳙来源预测算法
    """
    # 步骤1：计算ΔCt值
    delta_ct = {}
    for marker in ct_data:
        if ct_data[marker] == 'ND':  # 未检出
            delta_ct[marker] = 15.0
        else:
            delta_ct[marker] = ct_data[marker] - internal_ref_ct
    
    # 步骤2：特征标准化
    normalized_features = standardize_delta_ct(delta_ct)
    
    # 步骤3：模型预测
    if fish_type == '鲢鱼':
        model = load_ct_model('silver_carp_ct_tabpfn.pkl')
        threshold = 0.9949
    elif fish_type == '鳙鱼':
        model = load_ct_model('bighead_carp_ct_tabpfn.pkl')
        threshold = 0.9852
    
    prediction_prob = model.predict_proba(normalized_features)[0][1]
    
    # 步骤4：结果判定
    if prediction_prob > threshold:
        result = '野生'
        confidence = min(0.95, prediction_prob)
    else:
        result = '养殖'
        confidence = min(0.95, 1 - prediction_prob)
    
    return result, confidence, prediction_prob
```

[0028] 3.性能验证：

通过200个盲样验证实验，Ct值预测模型检测准确率达到：鲢鱼92.0%（敏感性94.0%，特异性90.0%），鳙鱼94.0%（敏感性96.0%，特异性92.0%）。与测序方案的一致性达到91.5%。批内重现性CV<5%，批间重现性CV<8%。与其他鱼类（草鱼、青鱼、鲤鱼、鲫鱼）无交叉反应，特异性>95%。检测限为10³ copies/g样本，检测时间2.5小时，检测成本约40元/样本。

[0029] 实施例3：便携式检测系统的构建与应用

[0030] 1.系统集成：

基于实施例1和实施例2的试剂盒和检测方法，构建了便携式鲢鳙来源检测系统，包括：DNA提取模块（快速提取设备，30分钟内完成）；多重PCR检测模块（便携式实时荧光定量PCR仪，集成试剂盒组分）；数据分析模块（内置Ct值预测模型软件，自动计算和判别结果）；结果输出模块（LCD显示屏和打印机，提供检测报告）。

[0031] 2.现场应用验证：

系统整体重量12kg，支持车载和现场检测。在5个不同地区的水产品批发市场进行现场检测验证，共检测样本500个，与实验室标准方法对比，一致性达91.2%。系统操作简便，非专业人员经过2小时培训即可熟练操作。

[0032] 以上仅为本发明的优选实施例而已，并不用于限制本发明，对于本领域的技术人员来说，本发明可以有各种更改和变化。凡在本发明的精神和原则之内，所作的任何修改、等同替换、改进等，均应包含在本发明的保护范围之内。

图1
[双技术路线检测流程示意图]

图2
[Ct值预测模型技术框图]

图3
[鲢鱼标志物检测结果ROC曲线]

图4
[鳙鱼标志物检测结果ROC曲线]

图5
[试剂盒验证实验混淆矩阵]

图6
[本发明试剂盒的技术结构组成图]

