#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LGBM递归特征消除 - 最终特征选择脚本 (简化版 v3.1)
集成Bootstrap稳定性评估 + GPU加速支持
基于2024年Mayo Clinic等顶级机构最佳实践

作者: AI Assistant  
日期: 2025-07-28
版本: 3.1 (Bootstrap-RFE + GPU加速)

主要功能:
1. 从Boruta脚本输出加载特征
2. 增强的相关性过滤 (去除相关性>0.9的特征)
3. Bootstrap稳定性评估的LGBM递归特征消除
4. GPU支持
5. 带分类约束的RFE (确保每个分类水平至少保留一个特征)
6. 实时进度监控和详细性能报告

Bootstrap-RFE工作原理:
- 进行B次Bootstrap循环 (默认10次)
- 每次循环中从训练集进行有放回抽样
- 在每个Bootstrap样本上完整运行一遍RFE流程
- 统计每个特征被选中的频率
- 选择频率最高的N个特征作为最终结果


文献依据:
- Mayo Clinic 2024: Nested CV and feature selection best practices
- Nature Precision Oncology 2024: Microbiome ML guidelines + Bootstrap
- PLOS ONE 2024: GPU-accelerated ML in bioinformatics
"""

import pandas as pd
import numpy as np
import os
import logging
import argparse
import sys
from sklearn.model_selection import GridSearchCV
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score
import lightgbm as lgb
import warnings
from datetime import datetime
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
from tqdm import tqdm
import psutil
import threading
from matplotlib.backends.backend_pdf import PdfPages
import matplotlib.patches as mpatches
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.font_manager as fm

# GPU优化环境设置
print("🚀 GPU强制模式启动")
print("⚡ GPU加速计算已启用")

# 尝试导入optuna，如果不可用则使用默认参数
try:
    import optuna  # 贝叶斯优化 (基于PLOS ONE 2024文献)
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False
    print("警告: optuna包未安装，将使用默认LGBM参数")

# GPU加速导入
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("✅ CuPy GPU支持已加载")
except ImportError:
    import numpy as cp
    GPU_AVAILABLE = False
    print("⚠️ CuPy未安装，将使用NumPy")

# 设置警告和日志
warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_taxonomic_level(feature_name: str) -> str:
    """
    从特征名中解析分类学水平
    
    分类学前缀映射:
    - p__: 门 (Phylum)
    - c__: 纲 (Class) 
    - o__: 目 (Order)
    - f__: 科 (Family)
    - g__: 属 (Genus)
    - s__: 种 (Species)
    """
    if not isinstance(feature_name, str):
        return 'unknown'
    
    # 检查是否包含分类学前缀
    taxonomic_prefixes = ['p__', 'c__', 'o__', 'f__', 'g__', 's__']
    
    if ';' in feature_name:
        # 完整分类学路径，查找最高级别的分类前缀
        parts = feature_name.split(';')
        for part in parts:
            if '__' in part:
                prefix = part.split('__')[0] + '__'
                if prefix in taxonomic_prefixes:
                    return prefix
    else:
        # 简化名称，直接检查前缀
        for prefix in taxonomic_prefixes:
            if feature_name.startswith(prefix):
                return prefix
    
    return 'unknown'

def bootstrap_sampling(n_samples, random_state=None):
    """使用CPU进行Bootstrap采样"""
    logger.debug("使用CPU (NumPy)进行Bootstrap采样")
    if random_state is not None:
        np.random.seed(random_state)
    indices = np.random.randint(0, n_samples, size=n_samples, dtype=np.int32)
    return indices

def correlation_matrix(X):
    """使用CPU进行相关性矩阵计算"""
    logger.debug("使用CPU (NumPy)进行相关性矩阵计算")
    try:
        # 使用NumPy进行高效计算
        corr_matrix = np.corrcoef(X.T, dtype=np.float32)
        return corr_matrix
    except Exception as cpu_error:
        logger.error(f"CPU相关性计算失败: {cpu_error}")
        # 如果NumPy失败，Pandas作为备用方案
        import pandas as pd
        df = pd.DataFrame(X)
        corr_matrix = df.corr().values.astype(np.float32)
        return corr_matrix

def clear_memory():
    """清理内存和GPU内存 - 增强错误处理"""
    import gc
    gc.collect()
    if GPU_AVAILABLE:
        try:
            cp.get_default_memory_pool().free_all_blocks()
            cp.get_default_pinned_memory_pool().free_all_blocks()
        except Exception as e:
            logger.debug(f"GPU内存清理失败: {e}")
            pass

def monitor_system_usage():
    """监控系统资源使用情况"""
    cpu_percent = psutil.cpu_percent(interval=1)
    memory_info = psutil.virtual_memory()
    
    logger.info(f"📊 系统资源状态:")
    logger.info(f"  CPU使用率: {cpu_percent:.1f}%")
    logger.info(f"  内存使用率: {memory_info.percent:.1f}%")
    
    # 尝试获取GPU信息
    try:
        import GPUtil
        gpus = GPUtil.getGPUs()
        if gpus:
            for i, gpu in enumerate(gpus):
                logger.info(f"  GPU {i}: {gpu.name}")
                logger.info(f"    GPU使用率: {gpu.load*100:.1f}%")
                logger.info(f"    GPU内存: {gpu.memoryUtil*100:.1f}%")
        else:
            logger.info("  GPU: 未检测到可用GPU")
    except ImportError:
        logger.info("  GPU: GPUtil未安装，无法监控GPU状态")
    except Exception as e:
        logger.info(f"  GPU: 监控失败 - {e}")

def check_gpu_availability():
    """检查GPU环境可用性 - 增强LightGBM GPU检测"""
    try:
        import lightgbm as lgb
        # 尝试创建一个简单的GPU数据集来测试
        test_data = lgb.Dataset(np.random.random((10, 5)), label=np.random.randint(0, 2, 10))
        test_params = {
            'objective': 'binary', 
            'device': 'gpu', 
            'verbose': -1,
            'gpu_platform_id': 0,
            'gpu_device_id': 0,
            'gpu_use_dp': False  # 使用单精度以节省GPU内存
        }
        # 修复API兼容性 
        model = lgb.train(test_params, test_data, num_boost_round=5, 
                         callbacks=[lgb.log_evaluation(0)])
        
        # 验证模型确实在GPU上训练
        if hasattr(model, 'params') and model.params.get('device') == 'gpu':
            logger.info("✅ LightGBM GPU环境检测成功，核心训练将在GPU上进行")
        else:
            logger.warning("⚠️ LightGBM GPU环境可用，但模型可能未在GPU上训练")
        
        return True
    except Exception as e:
        logger.error(f"❌ LightGBM GPU不可用: {e}")
        raise RuntimeError(f"LightGBM GPU强制要求但不可用: {e}. 请检查CUDA环境和LightGBM GPU支持")

class BootstrapLGBMRFE:
    """
    Bootstrap + LGBM递归特征消除器 (v3.1 - GPU优化版)
    
    集成Bootstrap稳定性评估的LGBM-RFE，专门针对微生物组数据优化
    支持GPU/CPU智能回退，核心LightGBM训练强制GPU
    """
    
    def __init__(self, n_final_features=6,
                 correlation_threshold=0.9, random_state=42,
                 bootstrap_n=10, use_gpu=True,
                 lgbm_params=None, early_stopping_rounds=10):
        """
        初始化Bootstrap-LGBM-RFE (GPU优化版)
        
        参数:
         - bootstrap_n: Bootstrap采样次数 (默认10次)
         - use_gpu: 是否使用GPU加速 (默认启用，自动回退到CPU)
        """
        self.n_final_features = n_final_features
        self.correlation_threshold = correlation_threshold
        self.random_state = random_state
        self.early_stopping_rounds = early_stopping_rounds
        self.bootstrap_n = bootstrap_n
        self.use_gpu = use_gpu
        
        # GPU强制检查 - 重点检查LightGBM GPU支持
        if self.use_gpu:
            check_gpu_availability()  # 如果LightGBM GPU不可用会直接抛出异常
            logger.info("✅ GPU模式已启用 - LightGBM将在GPU上训练")
            logger.info("✅ 辅助计算 (采样/相关性) 已切换到CPU")
        else:
            raise ValueError("此脚本强制要求GPU模式运行")
        
        # 基于2024年微生物组文献优化的LGBM参数 (GPU优化版)
        base_params = {
            'objective': 'multiclass',
            'metric': 'multi_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': 63,              # 文献推荐: 31-127, 微生物组常用63
            'learning_rate': 0.05,         # 提高学习率以配合GPU
            'max_depth': 11,               # 控制树深度，防止过拟合
            'feature_fraction': 0.8,       # 降低至0.8，适合高维稀疏数据
            'bagging_fraction': 0.8,       # 保持0.8
            'bagging_freq': 5,             # 保持5
            'min_child_samples': 20,       # 保持20
            'min_child_weight': 0.001,     # 防止过拟合
            'reg_alpha': 0.1,              # L1正则化，适合稀疏数据
            'reg_lambda': 0.1,             # L2正则化
            'subsample': 0.8,              # 子采样比例
            'colsample_bytree': 0.8,       # 列采样比例
            'verbose': -1,
            'random_state': random_state,
            'force_col_wise': True,        # 优化内存使用
            'is_unbalance': True,          # 处理类别不平衡
            'num_threads': 1,              # 限制CPU线程数，让GPU承担主要计算
            'data_random_seed': random_state,
            'feature_pre_filter': False    # 关闭预过滤以减少CPU负担
        }
        
        # GPU优化配置 - 强制GPU训练
        if self.use_gpu:
            base_params.update({
                'device': 'gpu',
                'gpu_platform_id': 0,
                'gpu_device_id': 0,
                'gpu_use_dp': False,           # 使用单精度浮点数，节省GPU内存
                'max_bin': 255,               # 优化GPU内存使用
                'max_cat_threshold': 32,      # 类别特征阈值
                'tree_learner': 'serial',     # GPU模式下推荐使用serial
                'histogram_pool_size': -1     # 让LightGBM自动管理GPU内存池
            })
            logger.info("🚀 LightGBM GPU训练配置已启用")
        else:
            base_params['device'] = 'cpu'
            
        self.lgbm_params = lgbm_params or base_params
        
        # 初始化组件
        self.label_encoder = None
        self.selected_features = None
        self.bootstrap_results = []  # 存储每次Bootstrap的结果
        self.feature_selection_frequency = {}  # 特征选择频率统计
        self.output_dir = None # 用于存储本次运行的输出目录
        self.train_data_original = None # 保存原始训练数据
        self.val_data_original = None # 保存原始验证数据
        
        logger.info("=== Bootstrap-LGBMRFE初始化完成 (v3.1 GPU优化版) ===")
        logger.info(f"目标特征数: {n_final_features}")
        logger.info(f"Bootstrap采样次数: {bootstrap_n} (训练集内采样)")
        logger.info(f"相关性阈值: {correlation_threshold}")
        logger.info(f"计算设备: LightGBM强制GPU + CPU辅助计算")
        logger.info("✅ 已删除交叉验证，直接使用训练集Bootstrap采样")
    
    def load_boruta_features(self, base_output_dir=None):
        """从Boruta脚本输出加载特征 (兼容新的文件命名格式)"""
        if base_output_dir is None:
            desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
            base_output_dir = os.path.join(desktop_path, "Boruta_Pipeline_V2_Output")

        elite_features_dir = os.path.join(base_output_dir, "2_Elite_Features_Data")

        # 查找Boruta输出文件 (支持新的命名格式)
        train_files = [f for f in os.listdir(elite_features_dir) 
                      if f.endswith('INTEGRATED_FEATURES_TRAIN.csv')]
        val_files = [f for f in os.listdir(elite_features_dir) 
                    if f.endswith('INTEGRATED_FEATURES_VALIDATION.csv')]

        if not train_files:
            raise FileNotFoundError(f"未找到训练数据文件在: {elite_features_dir}")
        if not val_files:
            raise FileNotFoundError(f"未找到验证数据文件在: {elite_features_dir}")

        # 选择第一个找到的文件 (或可以根据需要选择特定级别)
        train_file = os.path.join(elite_features_dir, train_files[0])
        val_file = os.path.join(elite_features_dir, val_files[0])

        logger.info(f"加载训练数据: {train_file}")
        logger.info(f"加载验证数据: {val_file}")

        # 加载训练和验证数据
        train_data = pd.read_csv(train_file)
        val_data = pd.read_csv(val_file)
        self.train_data_original = train_data.copy() # 保存原始副本
        self.val_data_original = val_data.copy() # 保存原始副本

        # 分离特征和标签
        feature_cols = [col for col in train_data.columns if col not in ['SampleID', 'Group']]

        # 确保验证集包含相同的特征
        val_feature_cols = [col for col in val_data.columns if col not in ['SampleID', 'Group']]
        if set(feature_cols) != set(val_feature_cols):
            logger.warning("训练集和验证集的特征不完全一致，使用交集")
            feature_cols = list(set(feature_cols) & set(val_feature_cols))

        X_train = train_data[feature_cols]
        y_train = train_data['Group']
        X_val = val_data[feature_cols]
        y_val = val_data['Group']

        # 编码标签
        self.label_encoder = LabelEncoder()
        y_train_encoded = self.label_encoder.fit_transform(y_train)
        y_val_encoded = self.label_encoder.transform(y_val)

        logger.info(f"成功加载数据:")
        logger.info(f"  特征数: {len(feature_cols)}")
        logger.info(f"  训练样本数: {len(X_train)}")
        logger.info(f"  验证样本数: {len(X_val)}")
        logger.info(f"  训练集类别分布: {dict(pd.Series(y_train).value_counts())}")
        logger.info(f"  验证集类别分布: {dict(pd.Series(y_val).value_counts())}")

        return X_train, X_val, y_train_encoded, y_val_encoded, feature_cols, y_train, y_val
    
    def correlation_filter(self, X, feature_names):
        """相关性过滤 - 使用CPU计算"""
        logger.info("开始相关性过滤 (CPU计算)...")
        
        # CPU加速相关性矩阵计算
        try:
            corr_matrix = correlation_matrix(X)
            logger.info("✅ 相关性矩阵计算完成")
        except Exception as e:
            logger.error(f"相关性矩阵计算失败: {e}")
            raise
        
        # 找到高度相关的特征对
        high_corr_pairs = []
        n_features = len(feature_names)
        
        for i in range(n_features):
            for j in range(i+1, n_features):
                if abs(corr_matrix[i, j]) > self.correlation_threshold:
                    high_corr_pairs.append((i, j, corr_matrix[i, j]))
        
        logger.info(f"发现 {len(high_corr_pairs)} 对高度相关的特征 (阈值: {self.correlation_threshold})")
        
        if len(high_corr_pairs) == 0:
            logger.info("无需进行相关性过滤")
            return X, feature_names
        
        # 使用LGBM计算特征重要性来决定保留哪个特征
        logger.info("使用LightGBM GPU计算特征重要性...")
        self.lgbm_params['num_class'] = len(np.unique(self.y_train_encoded))
        lgb_model = lgb.LGBMClassifier(**self.lgbm_params)
        lgb_model.fit(X, self.y_train_encoded)
        feature_importance = lgb_model.feature_importances_
        
        # 选择要移除的特征
        features_to_remove = set()
        for i, j, corr_val in high_corr_pairs:
            if feature_importance[i] < feature_importance[j]:
                features_to_remove.add(i)
                logger.debug(f"移除特征 {feature_names[i]} (相关性 {corr_val:.3f})")
            else:
                features_to_remove.add(j)
                logger.debug(f"移除特征 {feature_names[j]} (相关性 {corr_val:.3f})")
        
        # 应用过滤
        features_to_keep = [i for i in range(n_features) if i not in features_to_remove]
        X_filtered = X[:, features_to_keep]
        feature_names_filtered = [feature_names[i] for i in features_to_keep]
        
        logger.info(f"相关性过滤完成 (LightGBM GPU加速):")
        logger.info(f"  移除特征数: {len(features_to_remove)}")
        logger.info(f"  保留特征数: {len(feature_names_filtered)}")
        
        return X_filtered, feature_names_filtered
    
    def single_bootstrap_rfe(self, bootstrap_sample_data, y_bootstrap, feature_names, bootstrap_id):
        """单次Bootstrap的完整RFE流程"""
        current_features = feature_names.copy()
        current_X = bootstrap_sample_data.copy()
        
        # 更新LGBM参数中的类别数
        current_lgbm_params = self.lgbm_params.copy()
        current_lgbm_params['num_class'] = len(np.unique(y_bootstrap))
        
        # 统计初始分类水平分布
        initial_taxonomic_counts = {}
        for feature in current_features:
            level = parse_taxonomic_level(feature)
            initial_taxonomic_counts[level] = initial_taxonomic_counts.get(level, 0) + 1
        
        # 递归消除循环
        while len(current_features) > self.n_final_features:
            # 直接在Bootstrap样本上训练模型，无交叉验证
            train_data = lgb.Dataset(current_X, label=y_bootstrap)

            model = lgb.train(
                current_lgbm_params,
                train_data,
                num_boost_round=100,  # 减少训练轮数
                callbacks=[lgb.log_evaluation(0)]
            )
            
            # 验证模型是否在GPU上训练（仅在第一次Bootstrap时检查）
            if bootstrap_id == 0:
                if hasattr(model, 'params'):
                    device = model.params.get('device', 'unknown')
                    logger.info(f"🔍 验证: 模型正在 {device.upper()} 上训练")
                    if device != 'gpu':
                        logger.warning("⚠️ 警告: 模型未在GPU上训练，可能回退到CPU!")
                else:
                    logger.warning("⚠️ 无法验证模型训练设备")
            
            # 获取特征重要性
            mean_importance = model.feature_importance(importance_type='gain')
            
            # 如果已经达到目标特征数，停止
            if len(current_features) <= self.n_final_features:
                break
            
            # 分类约束特征移除 - 确保每个分类水平至少保留一个特征
            grouped_features = defaultdict(list)
            for i, feature in enumerate(current_features):
                level = parse_taxonomic_level(feature)
                grouped_features[level].append((i, feature))
            
            protected_indices = set()
            for level, features_in_level in grouped_features.items():
                if not features_in_level: 
                    continue
                
                # 如果某个分类水平只有一个特征，保护它
                if len(features_in_level) == 1:
                    protected_indices.add(features_in_level[0][0])
                else:
                    # 否则保护该级别中最重要的特征
                    level_importances = {idx: mean_importance[idx] for idx, _ in features_in_level}
                    most_important_idx = max(level_importances, key=level_importances.get)
                    protected_indices.add(most_important_idx)
            
            # 从候选中移除重要性最低的特征
            candidate_indices = set(range(len(current_features))) - protected_indices
            
            if not candidate_indices:
                # 所有特征都被保护，强制结束
                break
                
            candidate_importances = {idx: mean_importance[idx] for idx in candidate_indices}
            removed_idx = min(candidate_importances, key=candidate_importances.get)
            
            # 更新特征列表和矩阵
            current_features.pop(removed_idx)
            current_X = np.delete(current_X, removed_idx, axis=1)
        
        return current_features
    
    def bootstrap_rfe_pipeline(self, X_train, y_train_encoded, feature_names):
        """Bootstrap稳定性评估的RFE流程 - GPU优化版"""
        logger.info(f"=== 开始Bootstrap-RFE流程 (GPU优化) ===")
        logger.info(f"Bootstrap采样次数: {self.bootstrap_n}")
        logger.info(f"每次采样将从 {len(feature_names)} 个特征筛选到 {self.n_final_features} 个")
        logger.info(f"核心LightGBM训练: GPU强制模式")
        
        # Bootstrap开始前的系统状态
        logger.info("📊 Bootstrap开始前系统状态:")
        monitor_system_usage()
        
        bootstrap_results = []
        feature_count_history = []  # 记录每次Bootstrap的特征数量，用于过拟合评估
        
        # Bootstrap外循环，使用简洁的进度条
        with tqdm(range(self.bootstrap_n), 
                 desc="�� Bootstrap-RFE (GPU核心)", 
                 ncols=100,
                 ascii=True,
                 leave=True) as pbar:
            
            for bootstrap_id in pbar:
                # 使用CPU进行Bootstrap采样 (有放回抽样)
                n_samples = len(X_train)
                try:
                    bootstrap_indices = bootstrap_sampling(
                        n_samples, 
                        random_state=self.random_state + bootstrap_id
                    )
                except Exception as e:
                    logger.error(f"Bootstrap {bootstrap_id} CPU采样失败: {e}")
                    # 如果CPU采样都失败，这是严重问题，直接跳过本次循环
                    bootstrap_results.append([])
                    feature_count_history.append(0)
                    continue
                
                X_bootstrap = X_train.iloc[bootstrap_indices].values
                y_bootstrap = y_train_encoded[bootstrap_indices]
                
                # 在当前Bootstrap样本上运行完整RFE (LightGBM强制GPU)
                try:
                    selected_features = self.single_bootstrap_rfe(
                        X_bootstrap, y_bootstrap, feature_names, bootstrap_id
                    )
                    bootstrap_results.append(selected_features)
                    feature_count_history.append(len(selected_features))
                    
                    # 简洁的进度条更新
                    success_rate = len([r for r in bootstrap_results if r]) / len(bootstrap_results) * 100
                    pbar.set_postfix_str(f"成功率: {success_rate:.1f}% | GPU核心训练")
                    
                    # 定期清理内存和GPU内存 (每5次Bootstrap)
                    if bootstrap_id % 5 == 0:
                        clear_memory()
                    
                except Exception as e:
                    logger.warning(f"Bootstrap {bootstrap_id} RFE失败: {e}")
                    bootstrap_results.append([])  # 添加空结果
                    feature_count_history.append(0)
                    clear_memory()  # 失败时也清理内存
        
        # 过拟合评估
        logger.info("=== 过拟合评估 ===")
        valid_counts = [c for c in feature_count_history if c > 0]
        if valid_counts:
            mean_features = np.mean(valid_counts)
            std_features = np.std(valid_counts)
            cv_features = std_features / mean_features if mean_features > 0 else 0
            
            logger.info(f"特征数量稳定性:")
            logger.info(f"  平均特征数: {mean_features:.2f}")
            logger.info(f"  标准差: {std_features:.2f}")
            logger.info(f"  变异系数: {cv_features:.3f}")
            
            if cv_features < 0.1:
                logger.info("✅ 特征选择高度稳定，无过拟合风险")
            elif cv_features < 0.2:
                logger.info("⚠️ 特征选择较为稳定，轻微变异")
            else:
                logger.warning("❗ 特征选择变异较大，可能存在过拟合")
        
        # 统计特征选择频率
        logger.info("=== 计算特征选择频率 ===")
        all_features = list(set([feature for sublist in bootstrap_results for feature in sublist]))
        feature_frequency = {}
        
        for feature in all_features:
            count = sum(1 for feature_list in bootstrap_results if feature in feature_list)
            feature_frequency[feature] = count / len([r for r in bootstrap_results if r])  # 排除空结果
        
        # 按频率排序并选择最稳定的特征，同时确保每个分类水平至少保留一个特征
        sorted_features = sorted(feature_frequency.items(), key=lambda x: x[1], reverse=True)
        
        # 分类水平约束特征选择
        final_features = []
        level_counts = {}
        taxonomic_levels = ['p__', 'c__', 'o__', 'f__', 'g__', 's__']
        
        # 第一轮：为每个分类水平选择频率最高的特征
        used_features = set()
        for level in taxonomic_levels:
            level_features = [(f, freq) for f, freq in sorted_features 
                            if parse_taxonomic_level(f) == level and f not in used_features]
            if level_features:
                best_feature, best_freq = level_features[0]
                final_features.append(best_feature)
                used_features.add(best_feature)
                level_counts[level] = 1
                logger.info(f"为{level}水平选择保护特征: {best_feature} (频率: {best_freq:.3f})")
        
        # 第二轮：按频率填充剩余位置
        remaining_slots = self.n_final_features - len(final_features)
        for feature, freq in sorted_features:
            if len(final_features) >= self.n_final_features:
                break
            if feature not in used_features:
                final_features.append(feature)
                level = parse_taxonomic_level(feature)
                level_counts[level] = level_counts.get(level, 0) + 1
                used_features.add(feature)
        
        logger.info(f"分类水平约束特征选择完成，保证每个水平至少一个特征")
        
        logger.info("Bootstrap-RFE完成 (GPU优化):")
        logger.info(f"  成功的Bootstrap采样: {len([r for r in bootstrap_results if r])}/{self.bootstrap_n}")
        logger.info(f"  最终选择的 {len(final_features)} 个特征:")
        logger.info(f"  分类水平分布: {level_counts}")
        
        for i, feature in enumerate(final_features, 1):
            level = parse_taxonomic_level(feature)
            freq = feature_frequency.get(feature, 0)
            logger.info(f"    {i}. {feature} ({level}) - 频率: {freq:.3f}")
        
        # 保存结果供后续分析
        self.bootstrap_results = bootstrap_results
        self.feature_selection_frequency = feature_frequency
        
        return final_features
    
    def run_bootstrap_feature_selection(self, base_output_dir=None, optimize_hyperparams=False):
        """
        运行完整的Bootstrap-RFE流程，并保存结果
        """
        logger.info("=== 开始运行Bootstrap-RFE特征选择流程 ===")

        # 设置输出目录 (桌面独立文件夹)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
        self.output_dir = os.path.join(desktop_path, f"LGBM_RFE_Final_Features_{timestamp}")
        os.makedirs(self.output_dir, exist_ok=True)
        logger.info(f"输出目录已设置: {self.output_dir}")

        # 1. 加载Boruta筛选出的特征
        X_train, X_test, y_train_encoded, y_test_encoded, boruta_features, y_train_original, y_test_original = self.load_boruta_features(base_output_dir)
        self.y_train_encoded = y_train_encoded
        
        if optimize_hyperparams:
            self.lgbm_params = self.optimize_hyperparameters(X_train.values, y_train_encoded)

        # 2. 相关性过滤
        X_filtered, features_after_corr = self.correlation_filter(X_train.values, boruta_features)

        # 3. 如果特征数已经小于等于目标数，直接返回
        if len(features_after_corr) <= self.n_final_features:
            logger.warning("相关性过滤后特征数已小于或等于目标数，跳过RFE")
            self.selected_features = features_after_corr
            final_features = features_after_corr
        else:
            # 4. Bootstrap-RFE主流程
            final_features = self.bootstrap_rfe_pipeline(
                pd.DataFrame(X_filtered, columns=features_after_corr), 
                self.y_train_encoded, 
                features_after_corr
            )

        self.selected_features = final_features

        # 5. 提取最终特征数据并保存
        logger.info("=== 提取并保存最终特征数据 ===")
        self.extract_final_features_data()

        return final_features

    def optimize_hyperparameters(self, X, y, n_trials=30):
        """超参数优化 (简化版，适合Bootstrap流程)"""
        if not OPTUNA_AVAILABLE:
            logger.warning("Optuna未安装，跳过超参数优化")
            return self.lgbm_params

        logger.info(f"开始超参数优化 ({n_trials}次试验)...")

        def objective(trial):
            params = self.lgbm_params.copy()
            params.update({
                'num_leaves': trial.suggest_int('num_leaves', 31, 127),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.1),
                'max_depth': trial.suggest_int('max_depth', 6, 15),
                'feature_fraction': trial.suggest_float('feature_fraction', 0.7, 0.9),
                'bagging_fraction': trial.suggest_float('bagging_fraction', 0.7, 0.9),
                'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 0.3),
                'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 0.3),
                'num_class': len(np.unique(y))
            })

            # 直接在全部数据上训练，无交叉验证
            train_data = lgb.Dataset(X, label=y)

            model = lgb.train(
                params,
                train_data,
                num_boost_round=50,  # 减少轮数
                callbacks=[lgb.log_evaluation(0)]
            )

            # 使用训练准确率作为评估指标（简化版）
            y_pred = model.predict(X)
            y_pred_class = np.argmax(y_pred, axis=1)
            score = accuracy_score(y, y_pred_class)

            return score

        try:
            study = optuna.create_study(direction='maximize',
                                      sampler=optuna.samplers.TPESampler(seed=self.random_state))
            study.optimize(objective, n_trials=n_trials, show_progress_bar=True)

            # 更新最优参数
            best_params = self.lgbm_params.copy()
            best_params.update(study.best_params)
            best_params['num_class'] = len(np.unique(y))

            self.lgbm_params = best_params

            logger.info(f"超参数优化完成，最佳准确率: {study.best_value:.4f}")

        except Exception as e:
            logger.error(f"超参数优化失败: {e}")

            return self.lgbm_params

    def extract_final_features_data(self):
        """提取最终特征的原始数据并保存到桌面文件夹"""
        if self.selected_features is None:
            logger.error("尚未进行特征选择，无法提取特征数据")
            return None

        if self.train_data_original is None or self.val_data_original is None:
            logger.error("原始数据未加载，无法提取特征")
            return None

        logger.info("开始提取最终特征的原始数据...")

        train_data = self.train_data_original
        val_data = self.val_data_original

        # 提取最终特征
        final_columns = ['SampleID', 'Group'] + self.selected_features

        # 检查特征存在性
        missing_features = [f for f in self.selected_features
                           if f not in train_data.columns or f not in val_data.columns]
        if missing_features:
            logger.error(f"以下特征不存在: {missing_features}")
            return None

        # 提取数据并添加SampleID
        final_train_data = train_data[final_columns].copy()
        final_val_data = val_data[final_columns].copy()

        # 使用self.output_dir
        if self.output_dir is None:
            logger.error("输出目录未设置，无法保存数据文件。")
            return None

        # 保存最终特征数据到输出目录
        final_train_file = os.path.join(self.output_dir, 
                                       f"FINAL_FEATURES_TRAIN_{len(self.selected_features)}features.csv")
        final_val_file = os.path.join(self.output_dir, 
                                     f"FINAL_FEATURES_VALIDATION_{len(self.selected_features)}features.csv")

        final_train_data.to_csv(final_train_file, index=False)
        final_val_data.to_csv(final_val_file, index=False)

        # 保存特征选择频率报告
        frequency_report = pd.DataFrame([
            {'feature': feature, 'selection_frequency': freq}
            for feature, freq in sorted(self.feature_selection_frequency.items(), 
                                       key=lambda x: x[1], reverse=True)
        ])
        frequency_file = os.path.join(self.output_dir, "bootstrap_feature_frequencies.csv")
        frequency_report.to_csv(frequency_file, index=False)

        # 保存最终特征列表
        final_features_list = pd.DataFrame({
            'rank': range(1, len(self.selected_features) + 1),
            'feature': self.selected_features,
            'selection_frequency': [self.feature_selection_frequency.get(f, 0) for f in self.selected_features]
        })
        features_list_file = os.path.join(self.output_dir, "final_features_list.csv")
        final_features_list.to_csv(features_list_file, index=False)

        # 保存摘要信息
        summary_info = {
            'total_bootstrap_samples': self.bootstrap_n,
            'successful_samples': len([r for r in self.bootstrap_results if r]),
            'final_features_count': len(self.selected_features),
            'train_samples': len(final_train_data),
            'validation_samples': len(final_val_data),
            'computation_device': 'GPU' if self.use_gpu else 'CPU',
            'correlation_threshold': self.correlation_threshold,
            'bootstrap_samples': self.bootstrap_n,
            'creation_time': os.path.basename(self.output_dir).split('_')[-1]
        }
        
        import json
        summary_file = os.path.join(self.output_dir, "analysis_summary.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_info, f, indent=2, ensure_ascii=False)

        logger.info("Bootstrap-RFE结果保存完成 (数据和报告):")
        logger.info(f"  输出文件夹: {self.output_dir}")
        logger.info(f"  训练集: {final_train_file}")
        logger.info(f"  验证集: {final_val_file}")
        logger.info(f"  特征频率报告: {frequency_file}")
        logger.info(f"  特征列表: {features_list_file}")
        logger.info(f"  分析摘要: {summary_file}")
        logger.info(f"  最终特征数: {len(self.selected_features)}")

        return {
            'output_dir': self.output_dir,
            'train_file': final_train_file,
            'val_file': final_val_file,
            'frequency_file': frequency_file,
            'features_list_file': features_list_file,
            'summary_file': summary_file,
            'features': self.selected_features,
            'train_shape': final_train_data.shape,
            'val_shape': final_val_data.shape
        }

    def create_publication_visualizations(self, output_dir=None):
        """创建符合顶级期刊标准的可视化图表"""
        if output_dir is None:
            output_dir = "C:/Users/<USER>/Desktop/Boruta_Pipeline_V2_Output/2_Elite_Features_Data"
        
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 设置顶级期刊标准的matplotlib参数
        plt.style.use('default')  # 使用默认样式
        plt.rcParams.update({
            'font.family': 'Arial',
            'font.size': 12,
            'axes.linewidth': 1.2,
            'axes.spines.top': False,
            'axes.spines.right': False,
            'axes.grid': False,  # 删除背景网格
            'grid.alpha': 0,     # 确保网格完全透明
            'figure.dpi': 300,   # 高分辨率
            'savefig.dpi': 300,  # 保存时高分辨率
            'savefig.bbox': 'tight',
            'savefig.pad_inches': 0.1,
            'axes.labelsize': 14,
            'axes.titlesize': 16,
            'xtick.labelsize': 12,
            'ytick.labelsize': 12,
            'legend.fontsize': 12,
            'figure.titlesize': 18
        })

        # 创建PDF文件用于保存所有图表
        pdf_file = os.path.join(output_dir, f"bootstrap_rfe_visualizations_{timestamp}.pdf")

        with PdfPages(pdf_file) as pdf:
            # 1. 特征选择频率图
            self._create_feature_frequency_plot(pdf)

            # 2. 分类水平分布图
            self._create_taxonomic_distribution_plot(pdf)

            # 3. Bootstrap稳定性分析图
            self._create_bootstrap_stability_plot(pdf)

            # 4. 特征重要性热图
            self._create_feature_importance_heatmap(pdf)

        logger.info(f"📊 顶级期刊标准可视化图表已保存: {pdf_file}")

        # 同时保存单独的PNG文件
        self._save_individual_plots(output_dir, timestamp)

    def _create_feature_frequency_plot(self, pdf):
        """创建特征选择频率柱状图"""
        if not self.selected_features or not self.feature_selection_frequency:
            return

        fig, ax = plt.subplots(figsize=(12, 8))

        # 准备数据
        features = self.selected_features
        frequencies = [self.feature_selection_frequency.get(f, 0) for f in features]

        # 创建颜色映射
        colors = plt.cm.viridis(np.linspace(0.2, 0.8, len(features)))

        # 创建柱状图
        bars = ax.bar(range(len(features)), frequencies, color=colors,
                     edgecolor='black', linewidth=0.8, alpha=0.8)

        # 设置标签和标题
        ax.set_xlabel('Selected Features', fontweight='bold')
        ax.set_ylabel('Selection Frequency', fontweight='bold')
        ax.set_title('Bootstrap Feature Selection Frequency\n(n={} iterations)'.format(self.bootstrap_n),
                    fontweight='bold', pad=20)

        # 设置x轴标签
        feature_labels = [f.split('__')[-1][:15] + '...' if len(f.split('__')[-1]) > 15
                         else f.split('__')[-1] for f in features]
        ax.set_xticks(range(len(features)))
        ax.set_xticklabels(feature_labels, rotation=45, ha='right')

        # 添加数值标签
        for bar, freq in zip(bars, frequencies):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{freq:.3f}', ha='center', va='bottom', fontweight='bold')

        # 设置y轴范围
        ax.set_ylim(0, max(frequencies) * 1.15)

        # 移除网格和顶部、右侧边框
        ax.grid(False)
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)

        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight', dpi=300)
        plt.close(fig)

    def _create_taxonomic_distribution_plot(self, pdf):
        """创建分类水平分布饼图"""
        if not self.selected_features:
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        # 统计分类水平分布
        taxonomic_counts = {}
        for feature in self.selected_features:
            level = parse_taxonomic_level(feature)
            level_name = {
                'p__': 'Phylum', 'c__': 'Class', 'o__': 'Order',
                'f__': 'Family', 'g__': 'Genus', 's__': 'Species'
            }.get(level, level)
            taxonomic_counts[level_name] = taxonomic_counts.get(level_name, 0) + 1

        # 饼图1: 分类水平分布
        labels = list(taxonomic_counts.keys())
        sizes = list(taxonomic_counts.values())
        colors = plt.cm.Set3(np.linspace(0, 1, len(labels)))

        ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                startangle=90, textprops={'fontsize': 12, 'fontweight': 'bold'})

        ax1.set_title('Taxonomic Level Distribution\nof Selected Features',
                     fontweight='bold', fontsize=14, pad=20)

        # 柱状图2: 特征频率分布
        freq_values = [self.feature_selection_frequency.get(f, 0) for f in self.selected_features]
        ax2.hist(freq_values, bins=10, color='skyblue', alpha=0.7, edgecolor='black', linewidth=1.2)
        ax2.set_xlabel('Selection Frequency', fontweight='bold')
        ax2.set_ylabel('Number of Features', fontweight='bold')
        ax2.set_title('Distribution of Selection Frequencies', fontweight='bold', pad=20)

        # 移除网格
        ax2.grid(False)
        ax2.spines['top'].set_visible(False)
        ax2.spines['right'].set_visible(False)

        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight', dpi=300)
        plt.close(fig)

    def _create_bootstrap_stability_plot(self, pdf):
        """创建Bootstrap稳定性分析图"""
        if not self.bootstrap_results:
            return

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
            
        # 上图: Bootstrap迭代过程中的特征数量变化
        bootstrap_feature_counts = [len(result) if result else 0 for result in self.bootstrap_results]
        iterations = range(1, len(bootstrap_feature_counts) + 1)

        ax1.plot(iterations, bootstrap_feature_counts, 'b-', linewidth=2, alpha=0.7, label='Feature Count')
        ax1.axhline(y=self.n_final_features, color='red', linestyle='--', linewidth=2,
                   label=f'Target Features ({self.n_final_features})')

        ax1.set_xlabel('Bootstrap Iteration', fontweight='bold')
        ax1.set_ylabel('Number of Features Selected', fontweight='bold')
        ax1.set_title('Bootstrap Feature Selection Stability', fontweight='bold', pad=15)
        ax1.legend(frameon=False)
        ax1.grid(False)
        ax1.spines['top'].set_visible(False)
        ax1.spines['right'].set_visible(False)

        # 下图: 特征选择频率的累积分布
        if self.feature_selection_frequency:
            frequencies = list(self.feature_selection_frequency.values())
            frequencies.sort(reverse=True)

            ax2.plot(range(1, len(frequencies) + 1), frequencies, 'g-',
                    linewidth=2, marker='o', markersize=4, alpha=0.8)
            ax2.set_xlabel('Feature Rank', fontweight='bold')
            ax2.set_ylabel('Selection Frequency', fontweight='bold')
            ax2.set_title('Feature Selection Frequency Distribution', fontweight='bold', pad=15)
            ax2.grid(False)
            ax2.spines['top'].set_visible(False)
            ax2.spines['right'].set_visible(False)

        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight', dpi=300)
        plt.close(fig)

    def _create_feature_importance_heatmap(self, pdf):
        """创建特征重要性热图"""
        if not self.selected_features or not self.feature_selection_frequency:
            return

        fig, ax = plt.subplots(figsize=(14, 8))
        
        # 准备热图数据
        features = self.selected_features
        frequencies = [self.feature_selection_frequency.get(f, 0) for f in features]

        # 创建分类水平标签
        taxonomic_levels = []
        for feature in features:
            level = parse_taxonomic_level(feature)
            level_name = {
                'p__': 'Phylum', 'c__': 'Class', 'o__': 'Order',
                'f__': 'Family', 'g__': 'Genus', 's__': 'Species'
            }.get(level, level)
            taxonomic_levels.append(level_name)

        # 创建数据矩阵
        data_matrix = np.array(frequencies).reshape(1, -1)

        # 创建热图
        im = ax.imshow(data_matrix, cmap='YlOrRd', aspect='auto', vmin=0, vmax=1)

        # 设置标签
        feature_labels = [f.split('__')[-1][:20] + '...' if len(f.split('__')[-1]) > 20
                         else f.split('__')[-1] for f in features]

        ax.set_xticks(range(len(features)))
        ax.set_xticklabels(feature_labels, rotation=45, ha='right')
        ax.set_yticks([0])
        ax.set_yticklabels(['Selection Frequency'])

        # 添加数值标签
        for i in range(len(features)):
            ax.text(i, 0, f'{frequencies[i]:.3f}', ha='center', va='center',
                   color='white' if frequencies[i] > 0.5 else 'black', fontweight='bold')

        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Selection Frequency', rotation=270, labelpad=20, fontweight='bold')

        ax.set_title('Feature Selection Frequency Heatmap', fontweight='bold', pad=20)

        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight', dpi=300)
        plt.close(fig)

    def _save_individual_plots(self, output_dir, timestamp):
        """保存单独的PNG图片文件"""
        # 重新设置matplotlib参数以确保PNG输出质量
        plt.rcParams['savefig.format'] = 'png'
        plt.rcParams['savefig.dpi'] = 300

        # 保存特征频率图
        if self.selected_features and self.feature_selection_frequency:
            fig, ax = plt.subplots(figsize=(12, 8))

            features = self.selected_features
            frequencies = [self.feature_selection_frequency.get(f, 0) for f in features]
            colors = plt.cm.viridis(np.linspace(0.2, 0.8, len(features)))

            bars = ax.bar(range(len(features)), frequencies, color=colors,
                         edgecolor='black', linewidth=0.8, alpha=0.8)

            ax.set_xlabel('Selected Features', fontweight='bold')
            ax.set_ylabel('Selection Frequency', fontweight='bold')
            ax.set_title('Bootstrap Feature Selection Frequency\n(n={} iterations)'.format(self.bootstrap_n),
                        fontweight='bold', pad=20)

            feature_labels = [f.split('__')[-1][:15] + '...' if len(f.split('__')[-1]) > 15
                             else f.split('__')[-1] for f in features]
            ax.set_xticks(range(len(features)))
            ax.set_xticklabels(feature_labels, rotation=45, ha='right')

            for bar, freq in zip(bars, frequencies):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{freq:.3f}', ha='center', va='bottom', fontweight='bold')

            ax.set_ylim(0, max(frequencies) * 1.15)
            ax.grid(False)
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)

            plt.tight_layout()
            png_file = os.path.join(output_dir, f"feature_frequency_{timestamp}.png")
            plt.savefig(png_file, dpi=300, bbox_inches='tight')
            plt.close(fig)

            logger.info(f"📊 特征频率图已保存: {png_file}")

    def save_results(self):
        """保存完整的Bootstrap-RFE结果 (可视化部分)"""
        if self.output_dir is None:
            logger.error("输出目录未设置，无法保存可视化结果。")
            return
        
        # 创建顶级期刊标准的可视化图表
        logger.info("🎨 正在创建顶级期刊标准的可视化图表...")
        self.create_publication_visualizations(self.output_dir)


def parse_arguments():
    """解析命令行参数 (GPU强制模式)"""
    parser = argparse.ArgumentParser(
        description="Bootstrap-LGBM-RFE: GPU强制的Bootstrap稳定性特征选择",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
GPU强制模式 (默认配置):
  - 100次Bootstrap采样
  - GPU强制要求 (无GPU报错)
  - 6个最终特征
  - 每个分类水平至少保留一个特征
  
  # 自定义特征数
  python lgbm_rfe_optimized.py --final-features 8
        """)
    
    parser.add_argument('--final-features', type=int, default=6,
                       help='最终保留的特征数 (默认: 6)')
    
    return parser.parse_args()


def main():
    """主函数 - 运行Bootstrap-LGBM-RFE (GPU强制模式)"""
    # 解析命令行参数
    args = parse_arguments()
    
    logger.info("=== Bootstrap-LGBM-RFE v3.1 启动 ===")
    logger.info(f"配置: GPU强制模式, Bootstrap=100次, 目标特征={args.final_features}")
    logger.info("分类学前缀: p=门, c=纲, o=目, f=科, g=属, s=种")
    
    # 显示初始系统状态
    logger.info("📊 初始系统状态检查:")
    monitor_system_usage()
    
    # 创建Bootstrap-LGBM-RFE实例 (GPU强制模式)
    try:
        bootstrap_rfe = BootstrapLGBMRFE(
            n_final_features=args.final_features,
            correlation_threshold=0.9,
            bootstrap_n=100,  # 固定Bootstrap次数
            use_gpu=True,  # 强制GPU
        random_state=42
    )
    except (RuntimeError, ValueError) as e:
        logger.error(f"GPU初始化失败: {e}")
        print("\n❌ GPU环境检查失败!")
        print("请确保:")
        print("  1. 安装了支持GPU的LightGBM")
        print("  2. CUDA环境正确配置")
        print("  3. GPU内存充足")
        print("\n❌ GPU环境检查失败!")
        print("🚀 此脚本要求完整的GPU环境:")
        print("  必需组件:")
        print("  - ✅ CUDA驱动 (nvidia-smi检查)")
        print("  - ✅ LightGBM GPU版本")
        print("  - ✅ CuPy (pip install cupy-cuda11x)")
        print("  - 可选: GPUtil (pip install gputil)")
        print("\n🔧 安装顺序:")
        print("  1. 确保CUDA驱动正常")
        print("  2. pip install lightgbm --config-settings=cmake.define.USE_CUDA=ON")
        print("  3. pip install cupy-cuda11x  (根据CUDA版本)")
        print("  4. pip install gputil")
        return
    
    try:
        # 运行Bootstrap特征选择 (高精度模式)
        final_features = bootstrap_rfe.run_bootstrap_feature_selection(
            optimize_hyperparams=False  # 高精度模式下关闭超参数优化以节省时间
        )

        # 保存可视化结果
        bootstrap_rfe.save_results()

        print("\n" + "="*80)
        print("Bootstrap-LGBM-RFE完成！(v3.1 简化版，无交叉验证)")
        print("="*80)
        print(f"配置: GPU强制模式, 100次Bootstrap采样")
        print(f"最终选择的 {len(final_features)} 个高稳定性生物标志物:")
        
        # 显示特征及其选择频率和分类水平
        level_counts = {}
        for i, feature in enumerate(final_features, 1):
            freq = bootstrap_rfe.feature_selection_frequency.get(feature, 0)
            level = parse_taxonomic_level(feature)
            level_counts[level] = level_counts.get(level, 0) + 1
            
            level_name = {
                'p__': '门', 'c__': '纲', 'o__': '目', 
                'f__': '科', 'g__': '属', 's__': '种'
            }.get(level, level)
            
            print(f"  {i}. {feature} ({level_name}) - 频率: {freq:.3f}")
        
        print(f"\n分类水平分布: {level_counts}")
        print("="*80)
        print("✅ 简化版流程完成:")
        print("  - 第一阶段: Boruta快速特征选择")
        print("  - 第二阶段: 100次Bootstrap-LGBM-RFE稳定性评估")
        print("  - GPU强制计算 + 相关性过滤和分类约束")
        print("  - 每个分类水平至少保留一个特征")
        print("  - 删除交叉验证，直接在训练集Bootstrap样本上训练")
        print("  - 过拟合评估和稳定性分析")
        print("  - 最终特征数据已保存到桌面独立文件夹")
        print("="*80)
        
        # 显示最终系统状态
        logger.info("📊 流程完成后系统状态:")
        monitor_system_usage()
        
    except Exception as e:
        logger.error(f"Bootstrap-RFE过程中发生错误: {str(e)}")
        raise


if __name__ == "__main__":
    main()
