# microbiome_CL_WL_analysis.R
# CL组（养殖）与WL组（野生）微生物群落差异分析完整流程
# 重新设计：按组别整体分析，包含热图、LEfSe、火山图等高级分析

# ==============================================================================
# --- 0. 环境设置与包加载 ---
# ==============================================================================

# 清理环境
rm(list = ls())
gc()
# 加载必要的包
required_packages <- c(
  "ggplot2",        # 可视化
  "dplyr",          # 数据处理
  "tidyr",          # 数据整形
  "stringr",        # 字符串处理
  "ggpubr",         # 统计图表
  "patchwork",      # 图表组合
  "ggrepel",        # 标签避让
  "vegan",          # 生态学分析
  "RColorBrewer",   # 配色
  "scales",         # 图表缩放
  "pheatmap",       # 热图
  "cluster",        # 聚类分析
  "gridExtra",      # 图表布局
  "cowplot",        # 图表主题
  "viridis",        # 科学配色
  "circlize",       # 颜色映射
  "VennDiagram",    # 韦恩图
  "UpSetR",          # UpSet图
  "MASS",           # 线性判别分析
  "grid",           # 图形网格
  "microbiomeMarker" # LEfSe分析
)

# Bioconductor包单独处理
bioc_packages <- c("ComplexHeatmap", "phyloseq", "microbiomeMarker")

# 检查并安装缺失的CRAN包
missing_packages <- required_packages[!sapply(required_packages, requireNamespace, quietly = TRUE)]
if (length(missing_packages) > 0) {
  cat("安装缺失的CRAN包:", paste(missing_packages, collapse = ", "), "\n")
  install.packages(missing_packages, dependencies = TRUE)
}

# 检查并安装Bioconductor包
missing_bioc <- bioc_packages[!sapply(bioc_packages, requireNamespace, quietly = TRUE)]
if (length(missing_bioc) > 0) {
  cat("安装缺失的Bioconductor包:", paste(missing_bioc, collapse = ", "), "\n")
  if (!requireNamespace("BiocManager", quietly = TRUE)) {
    install.packages("BiocManager")
  }
  BiocManager::install(missing_bioc)
}

# 加载所有包（跳过无法加载的包）
all_packages <- c(required_packages, bioc_packages)
loaded_packages <- character()
failed_packages <- character()

for (pkg in all_packages) {
  tryCatch({
    suppressPackageStartupMessages(library(pkg, character.only = TRUE))
    loaded_packages <- c(loaded_packages, pkg)
  }, error = function(e) {
    failed_packages <- c(failed_packages, pkg)
    cat("警告: 无法加载包", pkg, "\n")
  })
}

cat("成功加载的包:", paste(loaded_packages, collapse = ", "), "\n")
if (length(failed_packages) > 0) {
  cat("未能加载的包:", paste(failed_packages, collapse = ", "), "\n")
  cat("注意: 某些高级功能可能不可用，但基本分析仍可进行\n")
}

cat("所有必要的包已加载完成\n\n")

# ==============================================================================
# --- 1. 数据配置与加载 ---
# ==============================================================================

cat("====================================================================\n")
cat("--- 步骤1: 数据加载与整合 ---\n")
cat("====================================================================\n")

# 数据文件配置（与2.R保持一致）
input_files <- list(
  'phylum' = 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y门.txt',
  'class' = 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y纲.txt',
  'order' = 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y目.txt',
  'family' = 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y科.txt',
  'genus' = 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y属.txt',
  'species' = 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y种.txt'
)

# 分类学水平名称
level_names <- c("phylum", "class", "order", "family", "genus", "species")

# 基本配置
sample_id_col <- "SampleID"
group_col <- "Group"
target_groups <- c("CY", "WY")

# 创建输出目录（修改为桌面路径）
base_output_dir <- file.path("C:/Users/<USER>/Desktop", "CY_WY_microbiome_analysis")
dir.create(base_output_dir, recursive = TRUE, showWarnings = FALSE)

plots_dir <- file.path(base_output_dir, "plots")
results_dir <- file.path(base_output_dir, "results")
dir.create(plots_dir, recursive = TRUE, showWarnings = FALSE)
dir.create(results_dir, recursive = TRUE, showWarnings = FALSE)

cat("输出目录已创建:\n")
cat("- 主目录:", normalizePath(base_output_dir), "\n")
cat("- 图片目录:", normalizePath(plots_dir), "\n")
cat("- 结果目录:", normalizePath(results_dir), "\n")

# 使用第一个文件来获取完整的样本ID和分组信息（与2.R保持一致）
base_info_file <- read.table(input_files[['phylum']], header = TRUE, sep = "\t", check.names = FALSE)
if(!group_col %in% colnames(base_info_file)) {
  stop(paste("错误: 分组列 '", group_col, "' 未在基础信息文件中找到。", sep=""))
}
if(!sample_id_col %in% colnames(base_info_file)) {
  stop(paste("错误: 样本ID列 '", sample_id_col, "' 未在基础信息文件中找到。", sep=""))
}

# 获取样本信息
sample_info <- base_info_file[, c(sample_id_col, group_col)]
sample_info <- sample_info[sample_info[[group_col]] %in% target_groups, ]

cat("样本信息加载完成:\n")
cat("- CY组样本数:", sum(sample_info[[group_col]] == "CY"), "\n")
cat("- WY组样本数:", sum(sample_info[[group_col]] == "WY"), "\n")

# Feature name simplification function
get_short_feature_name <- function(full_name) {
  # 类型检查和转换
  if (is.null(full_name)) return("Unknown")
  if (!is.character(full_name)) {
    full_name <- as.character(full_name)
  }
  if (is.na(full_name) || full_name == "" || full_name == "NA") return("Unknown")
  
  # Handle semicolon-separated taxonomic format (e.g., d__Bacteria;p__Acidobacteriota;c__Acidobacteriae;o__20CM-2-55-15;f__20CM-2-55-15)
  if (grepl(";", full_name)) {
    parts <- strsplit(full_name, ";")[[1]]
    if (length(parts) > 0) {
      last_part <- parts[length(parts)]
      if (last_part != "" && last_part != "unclassified") {
        # Keep underscores in taxonomic prefixes, only replace in species names
        if (grepl("^[a-z]__", last_part)) {
          # Split at the double underscore
          prefix_parts <- strsplit(last_part, "__")[[1]]
          if (length(prefix_parts) == 2) {
            prefix <- prefix_parts[1]
            species_name <- prefix_parts[2]
            # Replace underscores with spaces only in the species name part
            cleaned_species <- gsub("_", " ", species_name)
            return(paste0(prefix, "__", cleaned_species))
          }
        }
        return(last_part)
      }
    }
  }
  
  # Handle taxonomic prefixes like s__, g__, f__, etc. for single entries
  if (grepl("^[a-z]__", full_name)) {
    if (full_name != "" && full_name != "unclassified") {
      # Split at the double underscore
      prefix_parts <- strsplit(full_name, "__")[[1]]
      if (length(prefix_parts) == 2) {
        prefix <- prefix_parts[1]
        species_name <- prefix_parts[2]
        # Replace underscores with spaces only in the species name part
        cleaned_species <- gsub("_", " ", species_name)
        return(paste0(prefix, "__", cleaned_species))
      }
      return(full_name)
    }
  }
  
  # Split by double underscore for traditional format
  if (grepl("__", full_name)) {
    parts <- strsplit(full_name, "__")[[1]]
    if (length(parts) >= 2) {
      prefix <- parts[1]
      last_part <- parts[length(parts)]
      if (last_part != "" && !grepl("^[a-z]_", last_part)) {
        cleaned_species <- gsub("_", " ", last_part)
        return(paste0(prefix, "__", cleaned_species))
      }
    }
  }
  
  # 如果没有双下划线，直接清理下划线
  cleaned_name <- gsub("_", " ", full_name)
  if (cleaned_name != "") {
    return(cleaned_name)
  }
  
  return("Unknown")
}

# 数据加载和整合函数（与2.R保持一致的逻辑）
load_and_integrate_data <- function() {
  cat("开始加载和整合所有分类学水平的数据...\n")
  
  integrated_data <- list()
  
  for (i in 1:length(input_files)) {
    level <- level_names[i]
    file_path <- input_files[[i]]
    cat("加载", level, "水平数据:", file_path, "\n")
    
    if (!file.exists(file_path)) {
      cat("警告: 文件不存在:", file_path, "\n")
      next
    }
    
    # 读取数据
    data <- read.table(file_path, header = TRUE, sep = "\t", check.names = FALSE, 
                       stringsAsFactors = FALSE, comment.char = "")
    
    # 筛选目标组别
    data_filtered <- data[data[[group_col]] %in% target_groups, ]
    
    if (nrow(data_filtered) == 0) {
      cat("警告: 未找到目标组别数据\n")
      next
    }
    
    # 获取特征列
    feature_cols <- setdiff(colnames(data_filtered), c(sample_id_col, group_col))
    feature_matrix <- as.matrix(data_filtered[, feature_cols])
    feature_matrix[is.na(feature_matrix)] <- 0
    
    # 移除全零列
    non_zero_cols <- colSums(feature_matrix, na.rm = TRUE) > 0
    if (sum(non_zero_cols) == 0) {
      cat("警告: 所有特征列都为零\n")
      next
    }
    
    feature_cols <- feature_cols[non_zero_cols]
    feature_matrix <- feature_matrix[, non_zero_cols]
    
    # 简化特征名称
    short_names <- sapply(feature_cols, get_short_feature_name, USE.NAMES = FALSE)
    processed_names <- make.names(short_names, unique = TRUE)
    
    # 创建特征数据
    feature_data <- data.frame(
      data_filtered[, c(sample_id_col, group_col)],
      feature_matrix,
      stringsAsFactors = FALSE
    )
    colnames(feature_data)[3:ncol(feature_data)] <- processed_names
    
    # 存储数据
    integrated_data[[level]] <- list(
      data = feature_data,
      original_features = feature_cols,
      processed_features = processed_names,
      short_names = short_names,
      level = level
    )
    
    cat("  - 成功加载", nrow(feature_data), "个样本，", length(processed_names), "个特征\n")
  }
  
  # 创建整合的特征矩阵（所有水平合并）
  cat("\n创建整合的特征矩阵...\n")
  all_features_data <- sample_info
  
  for (level in names(integrated_data)) {
    level_data <- integrated_data[[level]]$data
    feature_cols <- integrated_data[[level]]$processed_features
    
    # 合并特征数据
    all_features_data <- merge(all_features_data, 
                               level_data[, c(sample_id_col, feature_cols)], 
                               by = sample_id_col, all.x = TRUE)
  }
  
  # 处理合并后的NA值
  feature_cols_all <- setdiff(colnames(all_features_data), c(sample_id_col, group_col))
  all_features_data[, feature_cols_all][is.na(all_features_data[, feature_cols_all])] <- 0
  
  cat("整合完成: ", nrow(all_features_data), "个样本，", length(feature_cols_all), "个特征\n")
  
  return(list(
    by_level = integrated_data,
    integrated = all_features_data,
    sample_info = sample_info
  ))
}

# 执行数据加载和整合
all_data <- load_and_integrate_data()

# 显示数据概况
cat("\n数据概况:\n")
cat("- CY组样本数:", sum(all_data$sample_info[[group_col]] == "CY"), "\n")
cat("- WY组样本数:", sum(all_data$sample_info[[group_col]] == "WY"), "\n")
cat("- 总特征数:", ncol(all_data$integrated) - 2, "\n")

for (level in names(all_data$by_level)) {
  level_features <- length(all_data$by_level[[level]]$processed_features)
  cat("  -", level, "水平:", level_features, "个特征\n")
}

cat("\n数据加载和整合完成\n\n")

# ==============================================================================
# --- 2. 可视化主题设置 ---
# ==============================================================================

cat("====================================================================\n")
cat("--- 步骤2: 可视化主题配置 ---\n")
cat("====================================================================\n")

# 顶刊级主题 - 增强版
theme_publication_enhanced <- function(base_size = 11, base_family = "") {
  theme_bw(base_size = base_size, base_family = base_family) +
    theme(
      # 背景设置
      panel.background = element_rect(fill = "white", colour = NA),
      plot.background = element_rect(fill = "white", colour = NA),
      
      # 边框和网格 - Prism风格，完全无网格
      panel.border = element_blank(),
      axis.line = element_line(colour = "black", size = 0.6),
      panel.grid.major = element_blank(),
      panel.grid.minor = element_blank(),
      
      # 分面设置
      strip.background = element_rect(colour = NA, fill = "grey95"),
      strip.text = element_text(face = "bold", size = rel(0.9), colour = "black", margin = margin(5, 5, 5, 5)),
      strip.placement = "outside",
      
      # 轴设置 - 更紧凑的边距
      axis.text = element_text(colour = "black", size = rel(0.85)),
      axis.title = element_text(colour = "black", size = rel(0.95), face = "bold"),
      axis.title.x = element_text(margin = margin(t = 5, r = 0, b = 0, l = 0)),
      axis.title.y = element_text(margin = margin(t = 0, r = 5, b = 0, l = 0)),
      axis.ticks = element_line(colour = "black", size = 0.5),
      axis.ticks.length = unit(2, "pt"),
      
      # 图例设置 - 更紧凑的图例
      legend.key = element_rect(colour = NA, fill = NA),
      legend.position = "bottom",
      legend.direction = "horizontal",
      legend.key.size = unit(0.3, "cm"),
      legend.spacing.x = unit(0.1, 'cm'),
      legend.margin = margin(t = 5, r = 0, b = 0, l = 0),
      legend.title = element_text(face = "bold", size = rel(0.85)),
      legend.text = element_text(size = rel(0.8)),
      legend.box.margin = margin(0, 0, 0, 0),
      
      # 标题设置
      plot.title = element_text(face = "bold", size = rel(1.15), hjust = 0.5,
                                margin = margin(b = 5), colour = "black"),
      plot.subtitle = element_text(size = rel(0.95), hjust = 0.5, colour = "grey30",
                                   margin = margin(b = 8)),
      plot.caption = element_text(size = rel(0.75), colour = "grey50", hjust = 1,
                                  margin = margin(t = 5)),
      
      # 边距设置 - 更紧凑的图边距
      plot.margin = margin(5, 5, 5, 5)
    )
}

# 保持向后兼容
theme_publication <- theme_publication_enhanced

# 科学配色方案集合 - 顶刊级优化
color_schemes <- list(
  # 组别对比配色 - 恢复为红/蓝组合
  group = c("CY" = "#E64B35CC", "WY" = "#4DBBD5CC"),
  
  # 显著性配色 - 恢复为红/蓝组合
  significance = c("Significantly Up" = "#E64B35", "Significantly Down" = "#4DBBD5", "Not Significant" = "#D3D3D3"),
  
  # LEfSe专用配色 - 使用组别配色
  lefse_groups = c("CY" = "#E64B35", "WY" = "#4DBBD5"),
  
  # 热图配色 - 科学级渐变，增加viridis
  heatmap = list(
    diverging = colorRampPalette(c("#4DBBD5", "white", "#E64B35"))(100),
    viridis = viridis::viridis(100),
    sequential_blue = c("#F7FBFF", "#DEEBF7", "#C6DBEF", "#9ECAE1", "#6BAED6",
                        "#4292C6", "#2171B5", "#08519C", "#08306B"),
    sequential_red = c("#FFF5F0", "#FEE0D2", "#FCBBA1", "#FC9272", "#FB6A4A",
                       "#EF3B2C", "#CB181D", "#A50F15", "#67000D")
  ),
  
  # 组成图配色 - 新增Nature期刊风格调色板
  composition = list(
    # Nature Journal风格 - 高对比度
    nature_journal = c(
      "#E64B35", "#4DBBD5", "#00A087", "#3C5488", "#F39B7F", "#8491B4",
      "#91D1C2", "#DC0000", "#7E6148", "#B09C85", "#FFC20A", "#0C7BDC",
      "#FEFE62", "#d55e00", "#0072b2", "#f0e442", "#cc79a7", "#56b4e9"
    ),
    # 主要调色板 - 高对比度
    primary = c("#E31A1C", "#1F78B4", "#33A02C", "#FF7F00", "#6A3D9A",
                "#A6CEE3", "#B2DF8A", "#FDBF6F", "#CAB2D6", "#FFFF99")
  ),
  
  # 多样性分析配色 - 优化版
  diversity = c("#2E8B57", "#CD853F", "#4682B4", "#DA70D6", "#32CD32", "#FF6347"),
  
  # Beta多样性配色 - 柔和版
  beta = c("#E64B35", "#4DBBD5", "#00A087", "#3C5488")
)

# 简称处理函数（与2.R脚本保持一致）
create_feature_name_mapping <- function(features) {
  name_correspondence <- data.frame(
    处理后特征名 = features,
    提取的原始简称 = sapply(features, function(f) {
      parts <- strsplit(f, "_")[[1]]
      if (length(parts) >= 2) {
        # 提取最后一部分作为简称，去除可能的前缀
        last_part <- parts[length(parts)]
        # 进一步简化，去除常见的分类学前缀
        simplified <- gsub("^[a-z]__", "", last_part)
        return(simplified)
      } else {
        return(f)
      }
    }),
    stringsAsFactors = FALSE
  )
  return(name_correspondence)
}

# 获取特征简称的函数
get_feature_short_name <- function(feature, name_correspondence) {
  idx <- which(name_correspondence$处理后特征名 == feature)
  if (length(idx) > 0) {
    return(name_correspondence$提取的原始简称[idx[1]])
  } else {
    return(feature)
  }
}

# 更新全局配色变量
colors_group <- color_schemes$group
colors_significance <- color_schemes$significance

# 设置全局主题 - 使用增强版
theme_set(theme_publication_enhanced())

# 辅助函数：同时保存PNG和PDF格式 - 顶刊级优化版
save_plot_both_formats <- function(filename_base, plot, width, height, dpi = 300) {
  # 保存高质量PNG格式 - 适合在线查看和演示
  ggsave(
    filename = paste0(filename_base, ".png"),
    plot = plot,
    width = width, height = height, dpi = dpi,
    bg = "white", device = "png",
    type = "cairo"  # 使用cairo设备获得更好的字体渲染
  )
  
  # 保存矢量PDF格式 - 适合印刷和发表
  ggsave(
    filename = paste0(filename_base, ".pdf"),
    plot = plot,
    width = width, height = height,
    bg = "white", device = "pdf",
    useDingbats = FALSE  # 避免字体问题
  )
  
  # 可选：保存高分辨率TIFF格式 - 适合期刊投稿
  if (dpi >= 600) {
    ggsave(
      filename = paste0(filename_base, "_highres.tiff"),
      plot = plot,
      width = width, height = height, dpi = dpi,
      bg = "white", device = "tiff",
      compression = "lzw"
    )
  }
}

# 专门用于LEfSe图的保存函数
save_lefse_plot <- function(filename_base, plot, width = 10, height = 10, dpi = 300) {
  # LEfSe圆形图需要正方形比例
  save_plot_both_formats(filename_base, plot, width, height, dpi)
  
  # 尝试保存SVG格式用于进一步编辑（如果svglite包可用）
  tryCatch({
    if (requireNamespace("svglite", quietly = TRUE)) {
      ggsave(
        filename = paste0(filename_base, ".svg"),
        plot = plot,
        width = width, height = height,
        bg = "white", device = "svg"
      )
    } else {
      cat("  - 注意: svglite包未安装，跳过SVG格式保存\n")
    }
  }, error = function(e) {
    cat("  - SVG保存失败:", e$message, "\n")
  })
}

cat("可视化主题配置完成\n\n")

# ==============================================================================
# --- 3. 样本概况分析 ---
# ==============================================================================

cat("====================================================================\n")
cat("--- 步骤3: CY组 vs WY组样本概况分析 ---\n")
cat("====================================================================\n")

perform_sample_overview <- function(data_obj) {
  cat("生成样本概况分析...\n")
  
  sample_info <- data_obj$sample_info
  
  # 样本数量统计
  sample_counts <- table(sample_info[[group_col]])
  
  # 创建样本数量柱状图 - 顶刊优化版
  count_data <- data.frame(
    Group = names(sample_counts),
    Count = as.numeric(sample_counts),
    stringsAsFactors = FALSE
  )
  
  p_sample_count <- ggplot(count_data, aes(x = Group, y = Count, fill = Group)) +
    geom_col(width = 0.5, alpha = 0.9, color = "black", size = 0.5) +
    geom_text(aes(label = Count), vjust = -0.3, size = 4, fontface = "bold", color = "black") +
    scale_fill_manual(values = colors_group) +
    scale_y_continuous(expand = expansion(mult = c(0, 0.15)),
                       breaks = pretty_breaks(n = 4)) +
    labs(
      title = "Sample Distribution",
      x = "Group",
      y = "Sample Count"
    ) +
    theme_publication_enhanced() +
    theme(
      legend.position = "none",
      axis.text.x = element_text(size = rel(0.9), face = "bold"),
      plot.title = element_text(size = rel(1.0), hjust = 0.5),
      axis.title = element_text(size = rel(0.9), face = "bold")
    )
  
  # 各分类学水平特征数量统计 - 顶刊优化版
  level_stats <- data.frame(
    Level = names(data_obj$by_level),
    Features = sapply(data_obj$by_level, function(x) length(x$processed_features)),
    stringsAsFactors = FALSE
  )
  
  # 创建渐变配色
  level_colors <- colorRampPalette(c("#2E8B57", "#90EE90"))(nrow(level_stats))
  
  p_feature_count <- ggplot(level_stats, aes(x = factor(Level, levels = c("phylum", "class", "order", "family", "genus", "species")), y = Features)) +
    geom_col(aes(fill = factor(Level, levels = c("phylum", "class", "order", "family", "genus", "species"))),
             alpha = 0.9, width = 0.5, color = "black", size = 0.5) +
    geom_text(aes(label = Features), vjust = -0.3, size = 3.5, fontface = "bold", color = "black") +
    scale_fill_manual(values = setNames(level_colors, c("phylum", "class", "order", "family", "genus", "species"))) +
    scale_y_continuous(expand = expansion(mult = c(0, 0.15)),
                       breaks = pretty_breaks(n = 4)) +
    labs(
      title = "Feature Distribution",
      x = "Taxonomic Level",
      y = "Feature Count"
    ) +
    theme_publication_enhanced() +
    theme(
      legend.position = "none",
      axis.text.x = element_text(angle = 45, hjust = 1, size = rel(0.8)),
      plot.title = element_text(size = rel(1.0), hjust = 0.5),
      axis.title = element_text(size = rel(0.9), face = "bold")
    )
  
  # 组合概况图 - 2图1列排版，不共用轴
  overview_combined <- p_sample_count / p_feature_count
  
  # 保存图表 - Prism风格尺寸，便于排版
  save_plot_both_formats(
    filename_base = file.path(plots_dir, "sample_overview"),
    plot = overview_combined,
    width = 4, height = 6
  )
  
  cat("样本概况图已保存:", normalizePath(file.path(plots_dir, "sample_overview.png")), "\n")
  
  # 安全显示
  tryCatch({
    print(overview_combined)
  }, error = function(e) {
    cat("注意: 图片显示失败（可能是窗口太小），但文件已成功保存\n")
  })
  
  # 保存统计信息
  overview_stats <- list(
    sample_counts = sample_counts,
    level_stats = level_stats,
    total_samples = nrow(sample_info),
    total_features = ncol(data_obj$integrated) - 2
  )
  
  write.csv(level_stats, file.path(results_dir, "feature_counts_by_level.csv"), row.names = FALSE)
  
  return(list(
    plots = list(sample_count = p_sample_count, feature_count = p_feature_count, combined = overview_combined),
    stats = overview_stats
  ))
}

# 执行样本概况分析
overview_results <- perform_sample_overview(all_data)

cat("样本概况分析完成\n\n")

# ==============================================================================
# --- 4. 整体分析（基于所有分类学水平整合数据）---
# ==============================================================================

cat("====================================================================\n")
cat("--- 步骤4: CY组 vs WY组整体分析（基于所有分类学水平整合数据）---\n")
cat("====================================================================\n")

# 4.1 整合Beta多样性分析
perform_integrated_beta_diversity <- function(integrated_data) {
  cat("执行整合Beta多样性分析（基于所有分类学水平）...\n")
  
  # 获取整合特征矩阵
  feature_cols <- setdiff(colnames(integrated_data), c(sample_id_col, group_col))
  feature_matrix <- as.matrix(integrated_data[, feature_cols])
  
  # 移除全零列
  col_sums <- colSums(feature_matrix, na.rm = TRUE)
  non_empty_cols <- col_sums > 0
  
  if (sum(non_empty_cols) < 10) {
    cat("警告: 整合数据有效特征数不足，跳过整合Beta多样性分析\n")
    return(NULL)
  }
  
  feature_matrix <- feature_matrix[, non_empty_cols]
  cat("使用", ncol(feature_matrix), "个整合特征进行分析\n")
  
  # 计算距离矩阵
  bray_dist <- vegdist(feature_matrix, method = "bray")
  
  # PCoA分析
  pcoa_bray <- cmdscale(bray_dist, eig = TRUE, k = 2)
  bray_var <- round(pcoa_bray$eig[1:2] / sum(pcoa_bray$eig[pcoa_bray$eig > 0]) * 100, 2)
  
  # 创建PCoA数据框
  pcoa_data <- data.frame(
    SampleID = integrated_data[[sample_id_col]],
    Group = integrated_data[[group_col]],
    PC1 = pcoa_bray$points[, 1],
    PC2 = pcoa_bray$points[, 2]
  )
  
  # PERMANOVA检验
  permanova_result <- adonis2(bray_dist ~ integrated_data[[group_col]], permutations = 999)
  
  # 提取PERMANOVA统计值
  permanova_r2 <- round(permanova_result$R2[1], 3)
  permanova_p <- permanova_result$`Pr(>F)`[1]
  permanova_p_text <- ifelse(permanova_p < 0.001, "p < 0.001", paste("p =", round(permanova_p, 3)))
  
  # 创建PCoA图
  p_integrated_pcoa <- ggplot(pcoa_data, aes(x = PC1, y = PC2, color = Group, fill = Group)) +
    geom_point(shape = 16, size = 2.5, alpha = 0.5) +
    stat_ellipse(geom = "polygon", level = 0.68, alpha = 0.15, aes(fill = Group)) +
    scale_color_manual(values = colors_group) +
    scale_fill_manual(values = colors_group) +
    labs(
      title = "Beta Diversity Analysis - PCoA (Bray-Curtis)",
      subtitle = paste("PERMANOVA: R² =", permanova_r2, ",", permanova_p_text),
      x = paste0("PC1 (", bray_var[1], "%)"),
      y = paste0("PC2 (", bray_var[2], "%)")
    ) +
    theme_publication() +
    theme(legend.position = "bottom")
  
  # NMDS分析
  tryCatch({
    nmds_result <- metaMDS(feature_matrix, distance = "bray", k = 2, trymax = 100, trace = FALSE)
    
    nmds_data <- data.frame(
      SampleID = integrated_data[[sample_id_col]],
      Group = integrated_data[[group_col]],
      NMDS1 = nmds_result$points[, 1],
      NMDS2 = nmds_result$points[, 2]
    )
    
    # 获取压力值
    stress_value <- round(nmds_result$stress, 3)
    
    p_integrated_nmds <- ggplot(nmds_data, aes(x = NMDS1, y = NMDS2, color = Group, fill = Group)) +
      geom_point(shape = 16, size = 2.5, alpha = 0.5) +
      stat_ellipse(geom = "polygon", level = 0.68, alpha = 0.15, aes(fill = Group)) +
      scale_color_manual(values = colors_group) +
      scale_fill_manual(values = colors_group) +
      labs(
        title = "Beta Diversity Analysis - NMDS",
        subtitle = paste("Stress =", stress_value, ", PERMANOVA: R² =", permanova_r2, ",", permanova_p_text),
        x = "NMDS1",
        y = "NMDS2"
      ) +
      theme_publication() +
      theme(legend.position = "bottom")
    
  }, error = function(e) {
    cat("NMDS分析失败，使用PCA替代\n")
    pca_result <- prcomp(feature_matrix, scale. = TRUE)
    pca_var <- round(summary(pca_result)$importance[2, 1:2] * 100, 2)
    
    nmds_data <- data.frame(
      SampleID = integrated_data[[sample_id_col]],
      Group = integrated_data[[group_col]],
      NMDS1 = pca_result$x[, 1],
      NMDS2 = pca_result$x[, 2]
    )
    
    p_integrated_nmds <- ggplot(nmds_data, aes(x = NMDS1, y = NMDS2, color = Group)) +
      geom_point(size = 2.5, alpha = 0.5, shape = 16) +
      stat_ellipse(level = 0.68, size = 1, alpha = 0.7, type = "norm") +
      scale_color_manual(values = colors_group) +
      labs(
        title = "Integrated Beta Diversity Analysis - PCA",
        x = paste0("PC1 (", pca_var[1], "%)"),
        y = paste0("PC2 (", pca_var[2], "%)")
      ) +
      theme_publication() +
      theme(legend.position = "bottom")
  })
  
  # 组合图
  integrated_beta_combined <- p_integrated_pcoa | p_integrated_nmds
  
  # 保存图表 - Prism风格尺寸
  save_plot_both_formats(
    filename_base = file.path(plots_dir, "integrated_beta_diversity"),
    plot = integrated_beta_combined,
    width = 8, height = 4
  )
  
  cat("整合Beta多样性图已保存:", normalizePath(file.path(plots_dir, "integrated_beta_diversity.png")), "\n")
  
  # 安全显示
  tryCatch({
    print(integrated_beta_combined)
  }, error = function(e) {
    cat("注意: 图片显示失败（可能是窗口太小），但文件已成功保存\n")
  })
  
  # 保存数据
  write.csv(pcoa_data, file.path(results_dir, "integrated_pcoa_data.csv"), row.names = FALSE)
  write.csv(nmds_data, file.path(results_dir, "integrated_nmds_data.csv"), row.names = FALSE)
  
  return(list(
    pcoa_data = pcoa_data,
    nmds_data = nmds_data,
    permanova = permanova_result,
    plots = list(pcoa = p_integrated_pcoa, nmds = p_integrated_nmds, combined = integrated_beta_combined)
  ))
}

# 4.2 整合差异丰度分析
perform_integrated_differential_analysis <- function(integrated_data) {
  cat("执行整合差异丰度分析（基于所有分类学水平）...\n")
  
  # 获取特征矩阵和分组信息
  feature_cols <- setdiff(colnames(integrated_data), c(sample_id_col, group_col))
  feature_matrix <- as.matrix(integrated_data[, feature_cols])
  groups <- integrated_data[[group_col]]
  
  # 移除全零列
  col_sums <- colSums(feature_matrix, na.rm = TRUE)
  non_empty_cols <- col_sums > 0
  feature_matrix <- feature_matrix[, non_empty_cols]
  feature_cols <- feature_cols[non_empty_cols]
  
  cat("使用", length(feature_cols), "个整合特征进行差异分析\n")
  
  # 初始化结果存储
  results_df <- data.frame(
    Feature = feature_cols,
    Level = sapply(feature_cols, function(x) strsplit(x, "_")[[1]][1]),
    CY_mean_abundance = numeric(length(feature_cols)),
    WY_mean_abundance = numeric(length(feature_cols)),
    log2FC = numeric(length(feature_cols)),
    p_value = numeric(length(feature_cols)),
    stringsAsFactors = FALSE
  )
  
  # 逐个特征进行检验
  for (i in 1:length(feature_cols)) {
    feature <- feature_cols[i]
    cy_values <- feature_matrix[groups == "CY", feature]
    wy_values <- feature_matrix[groups == "WY", feature]
    
    # 计算均值和log2FC
    cy_mean <- mean(cy_values, na.rm = TRUE)
    wy_mean <- mean(wy_values, na.rm = TRUE)
    results_df$CY_mean_abundance[i] <- cy_mean
    results_df$WY_mean_abundance[i] <- wy_mean
    
    # 为避免log2(0)问题，添加一个伪计数
    pseudo_count <- min(feature_matrix[feature_matrix > 0], na.rm = TRUE) * 0.01
    results_df$log2FC[i] <- log2((wy_mean + pseudo_count) / (cy_mean + pseudo_count))
    
    # Wilcoxon秩和检验
    if (length(cy_values) >= 3 && length(wy_values) >= 3) {
      test_res <- wilcox.test(cy_values, wy_values)
      results_df$p_value[i] <- test_res$p.value
    } else {
      results_df$p_value[i] <- 1
    }
  }
  
  # 多重检验校正
  results_df$p_adjusted <- p.adjust(results_df$p_value, method = "BH")
  
  # 添加显著性标签
  results_df$significance <- "Not Significant"
  results_df$significance[results_df$p_adjusted < 0.05 & results_df$log2FC > 1] <- "Significantly Up"
  results_df$significance[results_df$p_adjusted < 0.05 & results_df$log2FC < -1] <- "Significantly Down"
  
  # 简化特征名用于绘图，使用新的简称函数
  results_df$Feature_short <- sapply(results_df$Feature, get_short_feature_name)
  
  # 设置分类学水平的正确顺序
  taxonomic_order <- c("phylum", "class", "order", "family", "genus", "species")
  results_df$Level <- factor(results_df$Level, levels = taxonomic_order)
  
  # 选择每个分类学水平前3个最重要的特征进行标注(按p值和logFC排序)，确保每个级别都有代表
  top_features_by_level <- results_df %>%
      group_by(Level) %>%
      arrange(p_adjusted, desc(abs(log2FC))) %>%
      slice_head(n = 3) %>%
      ungroup() %>%
      mutate(Feature_short = sapply(Feature, get_short_feature_name))
  
  # 计算对称的x轴范围
  max_abs_log2fc <- max(abs(results_df$log2FC), na.rm = TRUE)
  x_limit <- ceiling(max_abs_log2fc * 1.1)  # 添加10%的边距
  
  # 绘制整合火山图
  p_integrated_volcano <- ggplot(results_df, aes(x = log2FC, y = -log10(p_adjusted), color = significance)) +
    geom_point(alpha = 0.6, size = 1.5, stroke = 0) +
    scale_color_manual(values = colors_significance) +
    scale_x_continuous(limits = c(-x_limit, x_limit)) +
    geom_vline(xintercept = c(-1, 1), linetype = "dashed", color = "grey50") +
    geom_hline(yintercept = -log10(0.05), linetype = "dashed", color = "grey50") +
    geom_text_repel(
      data = top_features_by_level,
      aes(label = Feature_short),
      size = 2.0,
      box.padding = 0.3,
      max.overlaps = 20,
      force = 2,
      min.segment.length = 0.1
    ) +
    labs(
      title = "Integrated Differential Abundance Analysis - Volcano Plot",
      x = "Log2 (Fold Change)",
      y = "-Log10 (Adjusted p-value)",
      color = "Significance"
    ) +
    theme_publication() +
    theme(
      legend.position = "bottom",
      legend.direction = "horizontal",
      legend.margin = margin(t = 8),
      plot.title = element_text(size = rel(1.0)),
      plot.subtitle = element_text(size = rel(0.8)),
      panel.grid.minor = element_blank()
    ) +
    guides(color = guide_legend(override.aes = list(size = 3)))
  
  # 按分类学水平分组的火山图
  p_integrated_volcano_by_level <- ggplot(results_df, aes(x = log2FC, y = -log10(p_adjusted), color = significance)) +
    geom_point(alpha = 0.6, size = 1.2, stroke = 0) +
    scale_color_manual(values = colors_significance) +
    scale_x_continuous(limits = c(-x_limit, x_limit)) +
    geom_vline(xintercept = c(-1, 1), linetype = "dashed", color = "grey50") +
    geom_hline(yintercept = -log10(0.05), linetype = "dashed", color = "grey50") +
    facet_wrap(~Level, scales = "free_y", ncol = 3) +
    labs(
      title = "Integrated Differential Abundance Analysis by Taxonomic Level",
      x = "Log2 (Fold Change)",
      y = "-Log10 (Adjusted p-value)",
      color = "Significance"
    ) +
    theme_publication() +
    theme(strip.text = element_text(size = 10))
  
  # 保存图表 - Prism风格尺寸
  save_plot_both_formats(
    plot = p_integrated_volcano,
    filename_base = file.path(plots_dir, "integrated_volcano_plot"),
    width = 6, height = 5
  )
  
  save_plot_both_formats(
    plot = p_integrated_volcano_by_level,
    filename_base = file.path(plots_dir, "integrated_volcano_by_level"),
    width = 8, height = 6
  )
  
  cat("整合差异分析火山图已保存\n")
  
  # 安全显示
  tryCatch({
    print(p_integrated_volcano)
  }, error = function(e) {
    cat("注意: 图片显示失败（可能是窗口太小），但文件已成功保存\n")
  })
  
  # 保存数据
  write.csv(results_df, file.path(results_dir, "integrated_differential_analysis.csv"), row.names = FALSE)
  
  # 统计显著特征
  sig_summary <- results_df %>%
    group_by(Level, significance) %>%
    summarise(count = n(), .groups = 'drop') %>%
    pivot_wider(names_from = significance, values_from = count, values_fill = 0)
  
  write.csv(sig_summary, file.path(results_dir, "integrated_differential_summary.csv"), row.names = FALSE)
  
  return(list(
    results = results_df,
    summary = sig_summary,
    plots = list(volcano = p_integrated_volcano, volcano_by_level = p_integrated_volcano_by_level)
  ))
}

# 4.3 整合Top特征分析
perform_integrated_top_features <- function(integrated_data) {
  cat("执行整合Top特征分析...\n")
  
  # 获取特征矩阵
  feature_cols <- setdiff(colnames(integrated_data), c(sample_id_col, group_col))
  feature_matrix <- as.matrix(integrated_data[, feature_cols])
  
  # 计算相对丰度
  rel_abundance <- sweep(feature_matrix, 1, rowSums(feature_matrix), "/")
  rel_abundance[is.na(rel_abundance)] <- 0
  
  # 计算各组平均相对丰度
  cy_samples <- integrated_data[[group_col]] == "CY"
  wy_samples <- integrated_data[[group_col]] == "WY"
  
  cy_mean <- colMeans(rel_abundance[cy_samples, , drop = FALSE], na.rm = TRUE)
  wy_mean <- colMeans(rel_abundance[wy_samples, , drop = FALSE], na.rm = TRUE)
  
  # 选择Top特征（按总丰度排序）
  total_abundance <- cy_mean + wy_mean
  top_features <- names(sort(total_abundance, decreasing = TRUE))[1:min(30, length(total_abundance))]
  
  # 准备组成数据
  comp_data <- data.frame(
    Feature = rep(top_features, 2),
    Group = rep(c("CY", "WY"), each = length(top_features)),
    Abundance = c(cy_mean[top_features], wy_mean[top_features]),
    Level = rep(sapply(top_features, function(x) strsplit(x, "_")[[1]][1]), 2),
    stringsAsFactors = FALSE
  )
  
  # 简化特征名称用于显示
  comp_data$Feature_short <- sapply(comp_data$Feature, get_short_feature_name)
  
  # 创建整合组成柱状图
  p_integrated_composition <- ggplot(comp_data, aes(x = reorder(Feature_short, Abundance), y = Abundance, fill = Group)) +
    geom_col(position = "dodge", alpha = 0.8, width = 0.7) +
    scale_fill_manual(values = colors_group) +
    coord_flip() +
    labs(
      title = "Integrated Top Features Composition Comparison",
      x = "Microbial Features",
      y = "Mean Relative Abundance",
      fill = "Group"
    ) +
    theme_publication() +
    theme(
      legend.position = "bottom",
      axis.text.y = element_text(size = 8)
    )
  
  # 按分类学水平分组的组成图
  p_integrated_composition_by_level <- ggplot(comp_data, aes(x = reorder(Feature_short, Abundance), y = Abundance, fill = Group)) +
    geom_col(position = "dodge", alpha = 0.8, width = 0.7) +
    scale_fill_manual(values = colors_group) +
    facet_wrap(~Level, scales = "free_y", ncol = 2) +
    coord_flip() +
    labs(
      title = "Integrated Top Features Composition by Taxonomic Level",
      x = "Microbial Features",
      y = "Mean Relative Abundance",
      fill = "Group"
    ) +
    theme_publication() +
    theme(
      legend.position = "bottom",
      axis.text.y = element_text(size = 6),
      strip.text = element_text(size = 9)
    )
  
  # 保存图表
  save_plot_both_formats(
    plot = p_integrated_composition,
    filename_base = file.path(plots_dir, "integrated_top_features"),
    width = 9, height = 7.5
  )
  
  save_plot_both_formats(
    plot = p_integrated_composition_by_level,
    filename_base = file.path(plots_dir, "integrated_top_features_by_level"),
    width = 10, height = 9
  )
  
  cat("整合Top特征分析图已保存\n")
  
  # 保存数据
  write.csv(comp_data, file.path(results_dir, "integrated_top_features.csv"), row.names = FALSE)
  
  return(list(
    data = comp_data,
    top_features = top_features,
    plots = list(composition = p_integrated_composition, composition_by_level = p_integrated_composition_by_level)
  ))
}

# 4.4 整合Alpha多样性分析
perform_integrated_alpha_diversity <- function(integrated_data) {
  cat("执行整合Alpha多样性分析（基于所有分类学水平）...\n")
  
  # 获取特征矩阵
  feature_cols <- setdiff(colnames(integrated_data), c(sample_id_col, group_col))
  feature_matrix <- as.matrix(integrated_data[, feature_cols])
  
  # 移除全零列
  col_sums <- colSums(feature_matrix, na.rm = TRUE)
  non_empty_cols <- col_sums > 0
  feature_matrix <- feature_matrix[, non_empty_cols]
  
  cat("使用", ncol(feature_matrix), "个整合特征进行Alpha多样性计算\n")
  
  # 计算多样性指数的函数
  calculate_diversity <- function(x) {
    # Shannon指数
    shannon <- -sum(x[x > 0] * log(x[x > 0]), na.rm = TRUE)
    
    # Simpson指数
    simpson <- 1 - sum((x[x > 0])^2, na.rm = TRUE)
    
    # 观察到的物种数（Observed）
    observed <- sum(x > 0, na.rm = TRUE)
    
    # Chao1指数（简化计算）
    singletons <- sum(x == 1, na.rm = TRUE)
    doubletons <- sum(x == 2, na.rm = TRUE)
    chao1 <- observed + ifelse(doubletons > 0, singletons^2 / (2 * doubletons), singletons * (singletons - 1) / 2)
    
    return(c(Shannon = shannon, Simpson = simpson, Observed = observed, Chao1 = chao1))
  }
  
  # 计算每个样本的多样性指数
  diversity_indices <- t(apply(feature_matrix, 1, calculate_diversity))
  
  # 创建多样性数据框
  alpha_data <- data.frame(
    SampleID = integrated_data[[sample_id_col]],
    Group = integrated_data[[group_col]],
    Level = "Integrated",
    diversity_indices,
    stringsAsFactors = FALSE
  )
  
  # 统计检验
  significance_results <- data.frame(
    Level = "Integrated",
    Index = c("Shannon", "Simpson", "Observed", "Chao1"),
    CY_mean = numeric(4),
    WY_mean = numeric(4),
    p_value = numeric(4),
    stringsAsFactors = FALSE
  )
  
  for (i in 1:4) {
    index_name <- c("Shannon", "Simpson", "Observed", "Chao1")[i]
    cy_values <- alpha_data[alpha_data$Group == "CY", index_name]
    wy_values <- alpha_data[alpha_data$Group == "WY", index_name]
    
    significance_results$CY_mean[i] <- mean(cy_values, na.rm = TRUE)
    significance_results$WY_mean[i] <- mean(wy_values, na.rm = TRUE)
    
    if (length(cy_values) >= 3 && length(wy_values) >= 3) {
      test_result <- wilcox.test(cy_values, wy_values)
      significance_results$p_value[i] <- test_result$p.value
    } else {
      significance_results$p_value[i] <- NA
    }
  }
  
  # 转换为长格式用于绘图
  alpha_long <- alpha_data %>%
    pivot_longer(cols = c("Shannon", "Simpson", "Observed", "Chao1"), 
                 names_to = "Index", values_to = "Value")
  
  # 创建整合Alpha多样性箱线图
  p_integrated_alpha_box <- ggplot(alpha_long, aes(x = Index, y = Value, fill = Group)) +
    geom_boxplot(alpha = 0.7, outlier.shape = 21, outlier.size = 1.5, width = 0.6, outlier.alpha = 0.8) +
    geom_point(position = position_jitterdodge(jitter.width = 0.2), 
               alpha = 0.4, size = 1.5) +
    stat_compare_means(aes(group = Group), method = "wilcox.test", label = "p.signif",
                       label.y.npc = 0.9, size = 5) +
    scale_fill_manual(values = colors_group) +
    labs(
      title = "Integrated Alpha Diversity Analysis",
      x = "Diversity Index",
      y = "Index Value",
      fill = "Group",
      caption = "Signif. codes: **** p < 0.0001, *** p < 0.001, ** p < 0.01, * p < 0.05"
    ) +
    theme_publication() +
    theme(legend.position = "bottom") +
    facet_wrap(~Index, scales = "free_y", ncol = 2, strip.position = "left")
  
  # 创建小提琴图
  p_integrated_alpha_violin <- ggplot(alpha_long, aes(x = Group, y = Value, fill = Group)) +
    geom_violin(alpha = 0.8, trim = FALSE) +
    geom_boxplot(width = 0.15, alpha = 0.8, outlier.alpha = 0.6) +
    geom_point(position = position_jitter(width = 0.05), alpha = 0.4, size = 1.0) +
    stat_compare_means(method = "wilcox.test", label = "p.signif",
                       label.y.npc = 0.9, size = 4) +
    scale_fill_manual(values = colors_group) +
    facet_wrap(~Index, scales = "free_y", ncol = 2, strip.position = "left") +
    labs(
      title = "Integrated Alpha Diversity Distribution Details",
      x = "Group",
      y = "Diversity Index Value",
      fill = "Group",
      caption = "Signif. codes: **** p < 0.0001, *** p < 0.001, ** p < 0.01, * p < 0.05"
    ) +
    theme_publication() +
    theme(
      legend.position = "bottom",
      strip.text = element_text(size = 10)
    )
  
  # 保存图表 - Prism风格尺寸
  save_plot_both_formats(
    plot = p_integrated_alpha_box,
    filename_base = file.path(plots_dir, "integrated_alpha_diversity_box"),
    width = 6, height = 4
  )
  
  save_plot_both_formats(
    plot = p_integrated_alpha_violin,
    filename_base = file.path(plots_dir, "integrated_alpha_diversity_violin"),
    width = 6, height = 5
  )
  
  cat("整合Alpha多样性分析图已保存\n")
  
  # 安全显示
  tryCatch({
    print(p_integrated_alpha_box)
  }, error = function(e) {
    cat("注意: 图片显示失败（可能是窗口太小），但文件已成功保存\n")
  })
  
  # 保存数据
  write.csv(alpha_data, file.path(results_dir, "integrated_alpha_diversity.csv"), row.names = FALSE)
  write.csv(significance_results, file.path(results_dir, "integrated_alpha_significance.csv"), row.names = FALSE)
  
  return(list(
    data = alpha_data,
    significance = significance_results,
    plots = list(box = p_integrated_alpha_box, violin = p_integrated_alpha_violin)
  ))
}

# 4.5 物种组成百分比分析（整合）
perform_integrated_composition_percentage <- function(integrated_data) {
  cat("执行整合物种组成百分比分析...\n")
  
  # 获取特征矩阵
  feature_cols <- setdiff(colnames(integrated_data), c(sample_id_col, group_col))
  feature_matrix <- as.matrix(integrated_data[, feature_cols])
  
  # 计算相对丰度（百分比）
  rel_abundance <- sweep(feature_matrix, 1, rowSums(feature_matrix), "/") * 100
  rel_abundance[is.na(rel_abundance)] <- 0
  
  # 按分类学水平分组特征（按门-种顺序）
  taxonomic_levels <- c("phylum", "class", "order", "family", "genus", "species")
  level_features <- list()
  for (level in taxonomic_levels) {
    level_cols <- grep(paste0("^", level, "_"), feature_cols, value = TRUE)
    if (length(level_cols) > 0) {
      level_features[[level]] <- level_cols
    }
  }
  
  # 为每个分类学水平创建堆叠柱状图
  composition_plots <- list()
  
  for (level in names(level_features)) {
    cat("创建", level, "水平组成百分比图...\n")
    
    level_cols <- level_features[[level]]
    level_abundance <- rel_abundance[, level_cols, drop = FALSE]
    
    # 计算各组平均相对丰度
    cy_samples <- integrated_data[[group_col]] == "CY"
    wy_samples <- integrated_data[[group_col]] == "WY"
    
    cy_mean <- colMeans(level_abundance[cy_samples, , drop = FALSE], na.rm = TRUE)
    wy_mean <- colMeans(level_abundance[wy_samples, , drop = FALSE], na.rm = TRUE)
    
    # Select top features (sorted by total abundance)
    total_abundance <- cy_mean + wy_mean
    top_n <- min(10, length(total_abundance))
    top_features <- names(sort(total_abundance, decreasing = TRUE))[1:top_n]
    
    # 计算"其他"类别
    cy_top_sum <- sum(cy_mean[top_features])
    wy_top_sum <- sum(wy_mean[top_features])
    cy_others <- 100 - cy_top_sum
    wy_others <- 100 - wy_top_sum
    
    # 准备堆叠数据
    stack_data <- data.frame(
      Feature = c(top_features, "Others"),
      CY = c(cy_mean[top_features], cy_others),
      WY = c(wy_mean[top_features], wy_others),
      stringsAsFactors = FALSE
    )
    
    # 简化特征名称
    stack_data$Feature_short <- sapply(stack_data$Feature, function(x) {
      if (x == "Others") return("Others")
      return(get_short_feature_name(x))
    })
    
    # 转换为长格式
    stack_long <- stack_data %>%
      pivot_longer(cols = c("CY", "WY"), names_to = "Group", values_to = "Percentage") %>%
      mutate(Feature_short = factor(Feature_short, levels = rev(c(stack_data$Feature_short))))
    
    # 修复配色设置 - 使用新的Nature Journal调色板
    unique_features <- unique(stack_data$Feature_short)
    n_colors <- length(unique_features)
    
    # 创建配色
    if ("Others" %in% unique_features) {
      non_others_features <- setdiff(unique_features, "Others")
      n_non_others <- length(non_others_features)
      
      # 从新调色板获取颜色
      feature_colors <- color_schemes$composition$nature_journal[1:n_non_others]
      
      # 组合颜色向量
      colors_stack <- c(feature_colors, "grey70") # Others 使用中性灰色
      names(colors_stack) <- c(non_others_features, "Others")
    } else {
      # 没有Others类别的情况
      colors_stack <- color_schemes$composition$nature_journal[1:n_colors]
      names(colors_stack) <- unique_features
    }
    
    # 创建堆叠柱状图 - Prism风格，缩小宽度
    p_stack <- ggplot(stack_long, aes(x = Group, y = Percentage, fill = Feature_short)) +
      geom_col(position = "stack", alpha = 0.9, width = 0.3, color = "white", size = 0.1) +
      scale_fill_manual(values = colors_stack) +
      scale_y_continuous(expand = c(0, 0), limits = c(0, 100),
                         breaks = seq(0, 100, 25),
                         labels = paste0(seq(0, 100, 25), "%")) +
      labs(
        title = "Taxonomic Composition",
        subtitle = paste(stringr::str_to_title(level_name), "Level - Relative Abundance"),
        x = "Group",
        y = "Relative Abundance (%)",
        fill = "Taxa"
      ) +
      theme_publication_enhanced() +
      theme(
        legend.position = "right",
        legend.key.size = unit(0.3, "cm"),
        legend.key.width = unit(0.3, "cm"),
        legend.key.height = unit(0.3, "cm"),
        legend.text = element_text(size = rel(0.7)),
        legend.title = element_text(size = rel(0.7), face = "bold"),
        legend.margin = margin(l = 5),
        axis.text.x = element_text(size = rel(0.9), face = "bold"),
        plot.title = element_text(size = rel(1.0)),
        plot.subtitle = element_text(size = rel(0.8)),
        panel.grid.major.x = element_blank(),
        panel.grid.minor.y = element_blank()
      ) +
      guides(fill = guide_legend(ncol = 1, byrow = TRUE,
                                 override.aes = list(size = 0.5)))
    
    composition_plots[[level]] <- p_stack
    
    # 保存图表
    save_plot_both_formats(
        plot = p_stack,
      filename_base = file.path(plots_dir, paste0("integrated_composition_percentage_", level)),
      width = 4, height = 4
      )
    cat(sprintf("  - %s 水平整合组成百分比图已保存\n\n", level))
    
    # 保存数据
    write.csv(stack_data, 
              file.path(results_dir, paste0("integrated_composition_percentage_", level, ".csv")),
              row.names = FALSE)
  }
  
  # 创建综合组成对比图（选择几个主要水平）
  if (length(composition_plots) > 0) {
    main_levels <- intersect(c("phylum", "class", "genus"), names(composition_plots))
    cat("Available composition plot levels:", paste(names(composition_plots), collapse = ", "), "\n")
    cat("Selected main levels:", paste(main_levels, collapse = ", "), "\n")
    
    if (length(main_levels) >= 2) {
      tryCatch({
        combined_composition <- wrap_plots(composition_plots[main_levels[1:min(3, length(main_levels))]], ncol = 1)
        
        save_plot_both_formats(
          plot = combined_composition,
          filename_base = file.path(plots_dir, "integrated_composition_percentage_combined"),
          width = 8, height = 12
        )
        
        cat("综合物种组成百分比图已保存\n")
      }, error = function(e) {
        cat("警告: 综合图保存失败:", e$message, "\n")
      })
    } else {
      cat("警告: 可用的主要分类学水平不足，跳过综合图创建\n")
    }
  } else {
    cat("警告: composition_plots为空，跳过综合图创建\n")
  }
  
  return(list(
    plots = composition_plots,
    levels = names(level_features)
  ))
}

# 执行整体分析
cat("4.1 整合Beta多样性分析\n")
integrated_beta_results <- perform_integrated_beta_diversity(all_data$integrated)

cat("\n4.2 整合差异丰度分析\n")
integrated_diff_results <- perform_integrated_differential_analysis(all_data$integrated)

cat("\n4.3 整合Top特征分析\n")
integrated_top_results <- perform_integrated_top_features(all_data$integrated)

cat("\n4.4 整合Alpha多样性分析\n")
integrated_alpha_results <- perform_integrated_alpha_diversity(all_data$integrated)

cat("\n4.5 整合物种组成百分比分析\n")
integrated_composition_results <- perform_integrated_composition_percentage(all_data$integrated)

cat("整体分析完成\n\n")

# ==============================================================================
# --- 5. 物种组成分析（分水平）---
# ==============================================================================

cat("====================================================================\n")
cat("--- 步骤5: CY组 vs WY组物种组成分析（分水平）---\n")
cat("====================================================================\n")

# 5.1 分水平物种组成百分比分析（增强版）
perform_composition_analysis_enhanced <- function(data_list) {
  composition_results <- list()
  composition_plots <- list()
  
  for (level_name in names(data_list)) {
    cat("分析", level_name, "水平物种组成...\n")
    
    data_obj <- data_list[[level_name]]
    data <- data_obj$data
    
    # 获取特征矩阵
    feature_cols <- setdiff(colnames(data), c(sample_id_col, group_col))
    feature_matrix <- as.matrix(data[, feature_cols])
    
    # 计算相对丰度（百分比）
    rel_abundance <- sweep(feature_matrix, 1, rowSums(feature_matrix), "/") * 100
    rel_abundance[is.na(rel_abundance)] <- 0
    
    # 计算各组平均相对丰度
    cy_samples <- data[[group_col]] == "CY"
    wy_samples <- data[[group_col]] == "WY"
    
    cy_mean <- colMeans(rel_abundance[cy_samples, , drop = FALSE], na.rm = TRUE)
    wy_mean <- colMeans(rel_abundance[wy_samples, , drop = FALSE], na.rm = TRUE)
    
    # Select top features (sorted by total abundance)
    total_abundance <- cy_mean + wy_mean
    top_n <- min(10, length(total_abundance))
    top_features <- names(sort(total_abundance, decreasing = TRUE))[1:top_n]
    
    # 计算"其他"类别
    cy_top_sum <- sum(cy_mean[top_features])
    wy_top_sum <- sum(wy_mean[top_features])
    cy_others <- 100 - cy_top_sum
    wy_others <- 100 - wy_top_sum
    
    # 准备堆叠数据
    stack_data <- data.frame(
      Feature = c(top_features, "Others"),
      CY = c(cy_mean[top_features], cy_others),
      WY = c(wy_mean[top_features], wy_others),
      stringsAsFactors = FALSE
    )
    
    # 简化特征名称用于显示
    stack_data$Feature_short <- sapply(stack_data$Feature, function(x) {
      if (x == "Others") return("Others")
      return(get_short_feature_name(x))
    })
    
    # 转换为长格式
    stack_long <- stack_data %>%
      pivot_longer(cols = c("CY", "WY"), names_to = "Group", values_to = "Percentage") %>%
      mutate(Feature_short = factor(Feature_short, levels = rev(c(stack_data$Feature_short))))
    
    # 修复配色设置 - 使用新的Nature Journal调色板
    unique_features <- unique(stack_data$Feature_short)
    n_colors <- length(unique_features)
    
    # 创建配色
    if ("Others" %in% unique_features) {
      non_others_features <- setdiff(unique_features, "Others")
      n_non_others <- length(non_others_features)
      
      # 从新调色板获取颜色
      feature_colors <- color_schemes$composition$nature_journal[1:n_non_others]
      
      # 组合颜色向量
      colors_stack <- c(feature_colors, "grey70") # Others 使用中性灰色
      names(colors_stack) <- c(non_others_features, "Others")
    } else {
      # 没有Others类别的情况
      colors_stack <- color_schemes$composition$nature_journal[1:n_colors]
      names(colors_stack) <- unique_features
    }
    
    # 创建堆叠柱状图 - Prism风格，缩小宽度
    p_stack <- ggplot(stack_long, aes(x = Group, y = Percentage, fill = Feature_short)) +
      geom_col(position = "stack", alpha = 0.9, width = 0.3, color = "white", size = 0.1) +
      scale_fill_manual(values = colors_stack) +
      scale_y_continuous(expand = c(0, 0), limits = c(0, 100),
                         breaks = seq(0, 100, 25),
                         labels = paste0(seq(0, 100, 25), "%")) +
      labs(
        title = "Taxonomic Composition",
        subtitle = paste(stringr::str_to_title(level_name), "Level - Relative Abundance"),
        x = "Group",
        y = "Relative Abundance (%)",
        fill = "Taxa"
      ) +
      theme_publication_enhanced() +
      theme(
        legend.position = "right",
        legend.key.size = unit(0.3, "cm"),
        legend.key.width = unit(0.3, "cm"),
        legend.key.height = unit(0.3, "cm"),
        legend.text = element_text(size = rel(0.7)),
        legend.title = element_text(size = rel(0.7), face = "bold"),
        legend.margin = margin(l = 5),
        axis.text.x = element_text(size = rel(0.9), face = "bold"),
        plot.title = element_text(size = rel(1.0)),
        plot.subtitle = element_text(size = rel(0.8)),
        panel.grid.major.x = element_blank(),
        panel.grid.minor.y = element_blank()
      ) +
      guides(fill = guide_legend(ncol = 1, byrow = TRUE,
                                 override.aes = list(size = 0.5)))
    
    # 准备对比数据（原有的对比柱状图）
    comp_data <- data.frame(
      Feature = rep(top_features, 2),
      Group = rep(c("CY", "WY"), each = length(top_features)),
      Abundance = c(cy_mean[top_features], wy_mean[top_features]),
      stringsAsFactors = FALSE
    )
    
    # 简化特征名称用于显示
    comp_data$Feature_short <- sapply(comp_data$Feature, get_short_feature_name)
    
    # 创建对比柱状图 - Prism风格
    p_composition <- ggplot(comp_data, aes(x = reorder(Feature_short, Abundance), y = Abundance, fill = Group)) +
      geom_col(position = position_dodge(width = 0.7), alpha = 0.9, width = 0.6,
               color = "black", size = 0.5) +
      scale_fill_manual(values = colors_group) +
      scale_y_continuous(expand = expansion(mult = c(0, 0.1)),
                         breaks = pretty_breaks(n = 4)) +
      coord_flip() +
      labs(
        title = "Taxonomic Abundance Comparison",
        x = "Microbial Taxa",
        y = "Mean Relative Abundance (%)",
        fill = "Group"
      ) +
      theme_publication_enhanced() +
      theme(
        legend.position = "bottom",
        legend.direction = "horizontal",
        legend.margin = margin(t = 8),
        axis.text.y = element_text(size = rel(0.8)),
        axis.text.x = element_text(size = rel(0.8)),
        plot.title = element_text(size = rel(1.0), hjust = 0.5),
        axis.title = element_text(size = rel(0.9), face = "bold")
      ) +
      guides(fill = guide_legend(override.aes = list(size = 0.5)))
    
    # --- 热图部分: 升级为L.R的ggplot2+patchwork方案 ---
    
    # 准备热图数据 (使用与堆叠图相同的Top N特征)
    heatmap_matrix <- rel_abundance[, top_features, drop = FALSE]
    
    # Z-score标准化 (按特征/列)
    heatmap_matrix_scaled <- scale(heatmap_matrix)
    heatmap_matrix_scaled[is.nan(heatmap_matrix_scaled)] <- 0 # 处理无方差的列
    
    # 准备绘图数据 - 添加数据验证
    heatmap_plot_data <- heatmap_matrix_scaled %>%
      as.data.frame() %>%
      mutate(SampleID = data[[sample_id_col]], Group = data[[group_col]]) %>%
      pivot_longer(cols = all_of(top_features), names_to = "Feature", values_to = "Z_Score") %>%
      mutate(Feature_short = sapply(Feature, get_short_feature_name))

    # 按组别和样本ID排序
    sample_order <- data %>%
        arrange(Group, SampleID) %>%
        pull(SampleID)
    
    heatmap_plot_data$SampleID <- factor(heatmap_plot_data$SampleID, levels = sample_order)
    heatmap_plot_data$Feature_short <- factor(heatmap_plot_data$Feature_short, levels = sapply(top_features, get_short_feature_name))

    # 创建主热图
    p_heatmap_main <- ggplot(heatmap_plot_data, aes(x = SampleID, y = Feature_short, fill = Z_Score)) +
      geom_tile(color = "white", size = 0.1) +
      scale_fill_gradient2(
        low = color_schemes$group["WY"], mid = "white", high = color_schemes$group["CY"],
        midpoint = 0, name = "Z-Score"
      ) +
      labs(y = "Taxa") +
      theme_publication_enhanced() +
      theme(
        axis.text.x = element_blank(),
        axis.ticks.x = element_blank(),
        axis.title.x = element_blank(),
        axis.text.y = element_text(size = 7),
        legend.position = "right",
        legend.key.size = unit(0.3, "cm"),
        legend.title = element_text(size = 7),
        legend.text = element_text(size = 6),
        plot.margin = margin(0, 2, 2, 2)
      )

    # 创建组别注释栏
    annotation_data <- data %>%
      dplyr::select(SampleID, Group) %>%
      distinct() %>%
      mutate(SampleID = factor(SampleID, levels = sample_order))
      
    p_annotation_bar <- ggplot(annotation_data, aes(x = SampleID, y = 1, fill = Group)) +
      geom_tile() +
      scale_fill_manual(values = setNames(c("#E64B35CC", "#4DBBD5CC"), c("CY", "WY"))) +
      theme_void() +
      theme(plot.margin = margin(2, 2, 0, 2))

    # 使用patchwork组合图表
    p_heatmap_ggplot <- p_annotation_bar / p_heatmap_main +
      plot_layout(heights = c(1, 10), guides = 'collect') & 
      theme(legend.position = 'right')

    composition_plots[[level_name]] <- list(
      composition = p_composition,
      stacked = p_stack,
      heatmap_ggplot = p_heatmap_ggplot
    )
    
    composition_results[[level_name]] <- list(
      composition_data = comp_data,
      stacked_data = stack_data,
      top_features = top_features
    )
    
    # 保存图表
    tryCatch({
      # 保存堆叠图
      save_plot_both_formats(
        plot = p_stack,
        filename_base = file.path(plots_dir, paste0("composition_stacked_", level_name)),
        width = 8, height = 8
      )
      
      # 保存对比图
      save_plot_both_formats(
        plot = p_composition,
        filename_base = file.path(plots_dir, paste0("composition_", level_name)),
        width = 10, height = 8
      )
      
      cat("  - 组成分析图片已保存:", paste0("composition_", level_name, ".png/.pdf"), "\n")
      cat("  - 堆叠图已保存:", paste0("composition_stacked_", level_name, ".png/.pdf"), "\n")
      
      # 只在图片保存成功后才尝试显示
      tryCatch({
        print(p_stack)
      }, error = function(e) {
        cat("  - 注意: 图片显示失败（可能是窗口太小），但文件已成功保存\n")
      })
      
    }, error = function(e) {
      cat("  - 警告: 组成分析图片保存失败:", e$message, "\n")
    })
    
    # 保存热图
    if (!is.null(composition_plots[[level_name]]$heatmap_ggplot)) {
      tryCatch({
        save_plot_both_formats(
          plot = p_heatmap_ggplot,
          filename_base = file.path(plots_dir, paste0("heatmap_ggplot_", level_name)),
          width = 8, height = 4
        )
        cat("  - ggplot2热图已保存:", paste0("heatmap_ggplot_", level_name, ".png/.pdf"), "\n")
      }, error = function(e) {
        cat("  - 警告: 热图保存失败:", e$message, "\n")
      })
    }
    
    # 保存数据
    write.csv(comp_data, 
              file.path(results_dir, paste0("composition_", level_name, ".csv")),
              row.names = FALSE)
    write.csv(stack_data, 
              file.path(results_dir, paste0("composition_stacked_", level_name, ".csv")),
              row.names = FALSE)
  }
  
  return(list(results = composition_results, plots = composition_plots))
}

# 执行增强的物种组成分析
composition_results <- perform_composition_analysis_enhanced(all_data$by_level)

cat("物种组成分析完成\n\n")

# ==============================================================================
# --- 6. Alpha多样性分析（分水平）---
# ==============================================================================

cat("====================================================================\n")
cat("--- 步骤6: CY组 vs WY组Alpha多样性分析（分水平）---\n")
cat("====================================================================\n")

perform_alpha_diversity <- function(data_list) {
  alpha_results <- list()
  alpha_plots <- list()
  
  # 计算多样性指数的函数
  calculate_diversity <- function(x) {
    # Shannon指数
    shannon <- -sum(x[x > 0] * log(x[x > 0]), na.rm = TRUE)
    
    # Simpson指数
    simpson <- 1 - sum((x[x > 0])^2, na.rm = TRUE)
    
    # 观察到的物种数（Observed）
    observed <- sum(x > 0, na.rm = TRUE)
    
    # Chao1指数（简化计算）
    singletons <- sum(x == 1, na.rm = TRUE)
    doubletons <- sum(x == 2, na.rm = TRUE)
    chao1 <- observed + ifelse(doubletons > 0, singletons^2 / (2 * doubletons), singletons * (singletons - 1) / 2)
    
    return(c(Shannon = shannon, Simpson = simpson, Observed = observed, Chao1 = chao1))
  }
  
  all_alpha_data <- data.frame()
  
  for (level_name in names(data_list)) {
    cat("计算", level_name, "水平Alpha多样性...\n")
    
    data_obj <- data_list[[level_name]]
    data <- data_obj$data
    
    # 获取特征矩阵
    feature_cols <- setdiff(colnames(data), c(sample_id_col, group_col))
    feature_matrix <- as.matrix(data[, feature_cols])
    
    # 计算每个样本的多样性指数
    diversity_indices <- t(apply(feature_matrix, 1, calculate_diversity))
    
    # 创建多样性数据框
    alpha_data <- data.frame(
      SampleID = data[[sample_id_col]],
      Group = data[[group_col]],
      Level = level_name,
      diversity_indices,
      stringsAsFactors = FALSE
    )
    
    all_alpha_data <- rbind(all_alpha_data, alpha_data)
    
    # 统计检验
    significance_results <- data.frame(
      Level = level_name,
      Index = c("Shannon", "Simpson", "Observed", "Chao1"),
      CY_mean = numeric(4),
      WY_mean = numeric(4),
      p_value = numeric(4),
      stringsAsFactors = FALSE
    )
    
    for (i in 1:4) {
      index_name <- c("Shannon", "Simpson", "Observed", "Chao1")[i]
      cy_values <- alpha_data[alpha_data$Group == "CY", index_name]
      wy_values <- alpha_data[alpha_data$Group == "WY", index_name]
      
      significance_results$CY_mean[i] <- mean(cy_values, na.rm = TRUE)
      significance_results$WY_mean[i] <- mean(wy_values, na.rm = TRUE)
      
      if (length(cy_values) >= 3 && length(wy_values) >= 3) {
        test_result <- wilcox.test(cy_values, wy_values)
        significance_results$p_value[i] <- test_result$p.value
      } else {
        significance_results$p_value[i] <- NA
      }
    }
    
    alpha_results[[level_name]] <- list(
      data = alpha_data,
      significance = significance_results
    )
    
    # 保存数据
    write.csv(alpha_data, 
              file.path(results_dir, paste0("alpha_diversity_", level_name, ".csv")),
              row.names = FALSE)
  }
  
  # 创建综合Alpha多样性图
  alpha_long <- all_alpha_data %>%
    pivot_longer(cols = c("Shannon", "Simpson", "Observed", "Chao1"), 
                 names_to = "Index", values_to = "Value")
  
  # 设置分类学水平的正确顺序
  taxonomic_order <- c("phylum", "class", "order", "family", "genus", "species")
  alpha_long$Level <- factor(alpha_long$Level, levels = taxonomic_order)
  
  # 计算显著性标识
  significance_labels <- alpha_long %>%
    group_by(Level, Index) %>%
    do({
      data <- .
      if (nrow(data) > 0) {
        test_result <- wilcox.test(Value ~ Group, data = data)
        p_val <- test_result$p.value
        sig_label <- ifelse(p_val < 0.001, "***",
                            ifelse(p_val < 0.01, "**",
                                   ifelse(p_val < 0.05, "*", "ns")))
        max_val <- max(data$Value, na.rm = TRUE)
        data.frame(y_pos = max_val * 1.1, label = sig_label)
      } else {
        data.frame(y_pos = 0, label = "ns")
      }
    }) %>%
    ungroup()
  
  # 创建箱线图
  p_alpha_box <- ggplot(alpha_long, aes(x = Level, y = Value, fill = Group)) +
    geom_boxplot(alpha = 0.8, outlier.shape = 21, outlier.size = 1.5, width = 0.6) +
    geom_point(position = position_jitterdodge(jitter.width = 0.2), 
               alpha = 0.4, size = 1.0) +
    stat_compare_means(aes(group = Group), method = "wilcox.test", label = "p.signif",
                       label.y.npc = 0.9, size = 4) +
    scale_fill_manual(values = colors_group) +
    facet_wrap(~Index, scales = "free_y", ncol = 2, strip.position = "left") +
    labs(
      title = "Alpha Diversity Comparison: CY vs WY Groups",
      subtitle = "Diversity indices across taxonomic levels",
      x = "Taxonomic Level",
      y = "Diversity Index Value",
      fill = "Group"
    ) +
    theme_publication() +
    theme(legend.position = "bottom")
  
  # 创建小提琴图
  p_alpha_violin <- ggplot(alpha_long, aes(x = Group, y = Value, fill = Group)) +
    geom_violin(alpha = 0.8, trim = FALSE) +
    geom_boxplot(width = 0.1, alpha = 0.8, outlier.alpha = 0.6) +
    geom_point(position = position_jitter(width = 0.05), alpha = 0.4, size = 1.0) +
    stat_compare_means(method = "wilcox.test", label = "p.signif", label.y.npc = 0.9, size = 4) +
    scale_fill_manual(values = colors_group) +
    facet_wrap(~Index, scales = "free_y", ncol = 2, strip.position = "left") +
    labs(
      title = "Alpha Diversity Distribution: CY vs WY Groups",
      subtitle = "Diversity indices across taxonomic levels",
      x = "Group",
      y = "Diversity Index Value",
      fill = "Group"
    ) +
    theme_publication() +
    theme(legend.position = "bottom")
  
  # 保存图表
  save_plot_both_formats(
    plot = p_alpha_box,
    filename_base = file.path(plots_dir, "alpha_diversity_box"),
    width = 9, height = 6
  )
  
  save_plot_both_formats(
    plot = p_alpha_violin,
    filename_base = file.path(plots_dir, "alpha_diversity_violin"),
    width = 9, height = 6
  )
  
  cat("Alpha多样性分析图已保存\n")
  
  # 安全显示
  tryCatch({
    print(p_alpha_box)
  }, error = function(e) {
    cat("注意: 图片显示失败（可能是窗口太小），但文件已成功保存\n")
  })
  
  # 保存数据
  write.csv(all_alpha_data, 
            file.path(results_dir, "alpha_diversity_all_levels.csv"),
            row.names = FALSE)
  
  return(list(
    results = alpha_results,
    plots = list(box = p_alpha_box, violin = p_alpha_violin)
  ))
}

# 执行Alpha多样性分析
alpha_results <- perform_alpha_diversity(all_data$by_level)

cat("Alpha多样性分析完成\n\n")

# ==============================================================================
# --- 7. 差异丰度分析（分水平）---
# ==============================================================================

cat("====================================================================\n")
cat("--- 步骤7: CY组 vs WY组差异丰度分析（分水平）---\n")
cat("====================================================================\n")

# 7.1 差异丰度分析
perform_differential_abundance_analysis <- function(data_obj) {
  cat("执行差异丰度分析...\n")
  
  diff_results <- list()
  diff_plots <- list()
  
  for (level_name in names(data_obj$by_level)) {
    cat("  - 分析", level_name, "水平...\n")
    
    tryCatch({
      level_data <- data_obj$by_level[[level_name]]
      feature_matrix <- as.matrix(level_data$processed_data[, level_data$processed_features])
      
      # 创建特征名称映射
      name_correspondence <- create_feature_name_mapping(level_data$processed_features)
      
      # 计算相对丰度
      rel_abundance <- sweep(feature_matrix, 1, rowSums(feature_matrix), "/")
      rel_abundance[is.na(rel_abundance)] <- 0
      
      # 分组数据
      cy_samples <- level_data$processed_data[[group_col]] == "CY"
      wy_samples <- level_data$processed_data[[group_col]] == "WY"
      
      # 统计检验
      results_df <- data.frame(
        Feature = level_data$processed_features,
        CY_mean = colMeans(rel_abundance[cy_samples, ], na.rm = TRUE),
        WY_mean = colMeans(rel_abundance[wy_samples, ], na.rm = TRUE),
        stringsAsFactors = FALSE
      )
      
      # 计算fold change和p值
      results_df$fold_change <- results_df$WY_mean / (results_df$CY_mean + 1e-10)
      results_df$log2FC <- log2(results_df$fold_change + 1e-10)
      
      # Wilcoxon检验
      results_df$p_value <- sapply(level_data$processed_features, function(feature) {
        cy_values <- rel_abundance[cy_samples, feature]
        wy_values <- rel_abundance[wy_samples, feature]
        
        if (sum(cy_values > 0) >= 3 && sum(wy_values > 0) >= 3) {
          test_result <- wilcox.test(cy_values, wy_values)
          return(test_result$p.value)
        } else {
          return(1)
        }
      })
      
      # 多重检验校正
      results_df$p_adjusted <- p.adjust(results_df$p_value, method = "BH")
      
      # 添加显著性标签
      results_df$significance <- "Not Significant"
      results_df$significance[results_df$p_adjusted < 0.05 & results_df$log2FC > 1] <- "Significantly Up"
      results_df$significance[results_df$p_adjusted < 0.05 & results_df$log2FC < -1] <- "Significantly Down"
      
      # 使用统一的简称处理
      results_df$Feature_short <- sapply(results_df$Feature, get_short_feature_name)
      
      diff_results[[level_name]] <- results_df
      
      # 绘制火山图 - 只标识最显著的前3个特征
      top_significant <- results_df %>%
        filter(significance != "Not Significant") %>%
        arrange(p_adjusted) %>%
        head(3)
      
      # 计算对称的x轴范围
      max_abs_log2fc_level <- max(abs(results_df$log2FC), na.rm = TRUE)
      x_limit_level <- ceiling(max_abs_log2fc_level * 1.1)  # 添加10%的边距
      
      p_volcano <- ggplot(results_df, aes(x = log2FC, y = -log10(p_adjusted), color = significance)) +
        geom_point(alpha = 0.7, size = 1.5, stroke = 0) +
        scale_color_manual(values = colors_significance, name = "Significance") +
        scale_x_continuous(limits = c(-x_limit_level, x_limit_level),
                           breaks = pretty_breaks(n = 5)) +
        scale_y_continuous(breaks = pretty_breaks(n = 4)) +
        geom_vline(xintercept = c(-1, 1), linetype = "dashed", color = "grey60", size = 0.4) +
        geom_hline(yintercept = -log10(0.05), linetype = "dashed", color = "grey60", size = 0.4) +
        geom_text_repel(
          data = top_significant,
          aes(label = Feature_short),
          size = 2.5,
          box.padding = 0.4,
          max.overlaps = 3,
          force = 2,
          min.segment.length = 0.1,
          fontface = "bold",
          segment.color = "grey30",
          segment.size = 0.3
        ) +
        labs(
          title = "Differential Abundance Analysis",
          x = "Log₂ (Fold Change)",
          y = "-Log₁₀ (Adjusted P-value)",
          color = "Significance"
        ) +
        theme_publication_enhanced() +
        theme(
          legend.position = "bottom",
          legend.direction = "horizontal",
          legend.margin = margin(t = 8),
          plot.title = element_text(size = rel(1.0), hjust = 0.5),
          axis.title = element_text(size = rel(0.9), face = "bold"),
          plot.subtitle = element_text(size = rel(0.8)),
          panel.grid.minor = element_blank()
        ) +
        guides(color = guide_legend(override.aes = list(size = 3)))
      
      diff_plots[[level_name]] <- p_volcano
      
      # 保存图表和数据 - Prism风格尺寸
      save_plot_both_formats(
        plot = p_volcano,
        filename_base = file.path(plots_dir, paste0("volcano_", level_name)),
        width = 5, height = 4
      )
      cat("  - 火山图已保存:", paste0("volcano_", level_name, ".png/.pdf"), "\n")
      
      write.csv(results_df,
                file.path(results_dir, paste0("differential_analysis_", level_name, ".csv")),
                row.names = FALSE)
      
    }, error = function(e) {
      cat("  - 错误:", level_name, "水平差异丰度分析失败:", e$message, "\n")
    })
  }
  return(list(results = diff_results, plots = diff_plots))
}

# 执行差异丰度分析
diff_results <- perform_differential_abundance_analysis(all_data$by_level)

cat("差异丰度分析完成\n\n")

# ==============================================================================
# --- 8. LEfSe 生物标志物发现 (新) ---
# ==============================================================================

cat("====================================================================\n")
cat("--- 步骤8: LEfSe 生物标志物发现 (使用microbiomeMarker) ---\n")
cat("====================================================================\n")

# LEfSe分析函数 - 使用microbiomeMarker包
perform_lefse_analysis_new <- function(integrated_data, p_value_threshold = 0.05, lda_score_threshold = 1.5) {
  cat("开始执行LEfSe分析 (microbiomeMarker)...\n")
  
  # 1. 数据准备：创建phyloseq对象
  cat("  - 步骤1: 准备phyloseq对象...\n")
  
  # a. OTU表
  feature_cols <- setdiff(colnames(integrated_data), c(sample_id_col, group_col))
  otu_matrix <- as.matrix(integrated_data[, feature_cols])
  rownames(otu_matrix) <- integrated_data[[sample_id_col]]
  
  # b. 样本数据
  sample_data <- data.frame(
    SampleID = integrated_data[[sample_id_col]],
    Group = integrated_data[[group_col]],
    row.names = integrated_data[[sample_id_col]]
  )
  
  # c. 分类学表
  # 解析特征名以创建分类学表
  tax_matrix <- do.call(rbind, strsplit(feature_cols, "_"))
  # 检查解析后矩阵的维度
  if (is.null(dim(tax_matrix)) || ncol(tax_matrix) < 2) {
    cat("  - 警告: 特征名无法有效解析为分类学信息，使用占位符\n")
    # 创建一个占位符分类学表
    tax_matrix <- matrix("Unknown", nrow = length(feature_cols), ncol = 6)
    colnames(tax_matrix) <- c("Phylum", "Class", "Order", "Family", "Genus", "Species")
  } else {
    # 假设第一个下划线前是分类水平
    tax_levels <- tax_matrix[, 1]
    tax_names <- apply(tax_matrix, 1, function(x) paste(x[-1], collapse = "_"))
    
    # 构建一个完整的分类学矩阵
    full_tax_matrix <- matrix(NA_character_, nrow = length(feature_cols), ncol = 6)
    colnames(full_tax_matrix) <- c("Phylum", "Class", "Order", "Family", "Genus", "Species")
    
    level_map <- c("phylum" = 1, "class" = 2, "order" = 3, "family" = 4, "genus" = 5, "species" = 6)
    
    for(i in 1:length(feature_cols)) {
      level_index <- level_map[tolower(tax_levels[i])]
      if (!is.na(level_index)) {
        full_tax_matrix[i, level_index] <- tax_names[i]
      }
    }
    tax_matrix <- full_tax_matrix
  }
  rownames(tax_matrix) <- feature_cols
  
  # 创建phyloseq对象
  ps_obj <- phyloseq(
    otu_table(otu_matrix, taxa_are_rows = FALSE),
    sample_data(sample_data),
    tax_table(tax_matrix)
  )
  
  cat("  - phyloseq对象创建完成\n")
  
  # 2. 运行LEfSe分析
  cat("  - 步骤2: 运行run_lefse...\n")
  lefse_results <- NULL
  tryCatch({
    lefse_results <- run_lefse(
      ps_obj,
      group = "Group",
      taxa_rank = "all", # "all" 表示分析所有级别
      norm = "CPM", # 使用CPM进行标准化
      kw_cutoff = p_value_threshold,
      lda_cutoff = lda_score_threshold
    )
    cat("  - run_lefse执行成功\n")
  }, error = function(e) {
    cat("  - 错误: run_lefse执行失败:", e$message, "\n")
    return(NULL) # 返回NULL表示失败
  })
  
  if (is.null(lefse_results) || is.null(marker_table(lefse_results)) || nrow(marker_table(lefse_results)) == 0) {
    cat("  - LEfSe分析未找到任何显著的生物标志物。\n")
    return(NULL)
  }
  
  cat("  - 找到", nrow(marker_table(lefse_results)), "个生物标志物\n")
  
  # 3. 可视化
  cat("  - 步骤3: 生成可视化图表...\n")
  
  # a. 分支图 (Cladogram)
  p_cladogram <- plot_cladogram(lefse_results, color = color_schemes$group, clade_label_level = 4) +
    theme(legend.position = "bottom")
  
  # b. 柱状图
  p_marker_bar <- plot_marker_bar(lefse_results)
  
  # 4. 保存结果
  cat("  - 步骤4: 保存图表和数据...\n")
  output_dir <- file.path(base_output_dir, "8_LEfSe_Analysis_New")
  dir.create(output_dir, showWarnings = FALSE, recursive = TRUE)
  
  # 保存分支图 - Prism风格尺寸
  save_plot_both_formats(
    filename_base = file.path(output_dir, "lefse_cladogram"),
    plot = p_cladogram,
    width = 6, height = 6
  )
  cat("  - 分支图已保存\n")
  
  # 保存柱状图 - Prism风格尺寸
  plot_height <- max(3, 0.3 + 0.15 * nrow(marker_table(lefse_results)))
  save_plot_both_formats(
    filename_base = file.path(output_dir, "lefse_marker_bar"),
    plot = p_marker_bar,
    width = 5, height = plot_height
  )
  cat("  - 标志物柱状图已保存\n")
  
  # 保存数据
  write.csv(marker_table(lefse_results),
            file.path(output_dir, "lefse_biomarkers_results.csv"),
            row.names = FALSE)
  cat("  - 结果数据已保存\n")
  
  return(list(
    results = lefse_results,
    cladogram = p_cladogram,
    marker_bar = p_marker_bar
  ))
}

# 执行LEfSe分析 - 使用整体数据
lefse_results <- perform_lefse_analysis_new(all_data$integrated)

cat("LEfSe分析完成\n\n")

# ==============================================================================
# --- 9. Venn图分析 ---
# ==============================================================================

cat("====================================================================\n")
cat("--- 步骤9: 组间Venn图分析 (CY vs WY) ---\n")
cat("====================================================================\n")

# 新的Venn图分析函数，比较CY和WY组
perform_group_venn_analysis <- function(integrated_data) {
  cat("开始执行组间Venn图分析 (CY vs WY)...\n")
  
  # 1. 准备数据
  feature_cols <- setdiff(colnames(integrated_data), c(sample_id_col, group_col))
  # 确保特征矩阵中没有非数值列
  is_numeric_col <- sapply(integrated_data[, feature_cols], is.numeric)
  if(any(!is_numeric_col)) {
    cat("  - 警告: 发现非数值特征列，已移除:", paste(feature_cols[!is_numeric_col], collapse=", "), "\n")
    feature_cols <- feature_cols[is_numeric_col]
  }
  
  feature_matrix <- as.matrix(integrated_data[, feature_cols])
  
  # 获取CY组和WY组的样本
  cy_samples <- integrated_data$Group == "CY"
  wy_samples <- integrated_data$Group == "WY"
  
  # 2. 提取每组中存在的特征（丰度 > 0）
  cy_features <- feature_cols[colSums(feature_matrix[cy_samples, , drop = FALSE], na.rm = TRUE) > 0]
  wy_features <- feature_cols[colSums(feature_matrix[wy_samples, , drop = FALSE], na.rm = TRUE) > 0]
  
  cat("  - CY组发现", length(cy_features), "个特征\n")
  cat("  - WY组发现", length(wy_features), "个特征\n")
  
  # 3. 创建Venn图
  venn_list <- list(
    CY = cy_features,
    WY = wy_features
  )
  
  # 创建输出目录
  venn_dir <- file.path(base_output_dir, "9_Group_Venn_Analysis")
  dir.create(venn_dir, showWarnings = FALSE, recursive = TRUE)
  
  # 使用VennDiagram包创建Venn图
  venn.diagram(
    x = venn_list,
    category.names = c("Cultured (CY)", "Wild (WY)"),
    filename = file.path(venn_dir, "venn_CY_vs_WY.png"),
    output = TRUE,
    
    # 样式
    scaled = TRUE,
    imagetype = "png",
    height = 1000, 
    width = 1000, 
    resolution = 300,
    compression = "lzw",
    
    # 圆圈
    lwd = 2,
    col = c(color_schemes$group["CY"], color_schemes$group["WY"]),
    fill = c(alpha(color_schemes$group["CY"], 0.3), alpha(color_schemes$group["WY"], 0.3)),
    
    # 数字
    cex = 1.5,
    fontface = "bold",
    fontfamily = "sans",
    
    # 类别标签
    cat.cex = 1.2,
    cat.fontface = "bold",
    cat.default.pos = "outer",
    cat.pos = c(-20, 20),
    cat.dist = c(0.055, 0.055),
    cat.fontfamily = "sans"
  )
  
  # 绘制并保存Venn图为PDF
  venn.diagram(
    x = list(CY = cy_features, WY = wy_features),
    filename = file.path(venn_dir, "venn_CY_vs_WY.pdf"),
    output = TRUE,
    scaled = TRUE,
    col = "transparent",
    fill = c(color_schemes$group["CY"], color_schemes$group["WY"]),
    alpha = 0.6,
    label.col = "white",
    cex = 1.5,
    fontfamily = "sans",
    fontface = "bold",
    cat.col = c(color_schemes$group["CY"], color_schemes$group["WY"]),
    cat.cex = 1.5,
    cat.fontfamily = "sans",
    margin = 0.1,
    main = "Feature Venn Diagram (CY vs WY)",
    main.cex = 1.2
  )
  
  # 4. 保存特征列表
  # 使用内置的intersect, setdiff来获取列表
  shared_features <- intersect(cy_features, wy_features)
  cy_only_features <- setdiff(cy_features, wy_features)
  wy_only_features <- setdiff(wy_features, cy_features)
  
  # 将列表写入文件
  capture.output(
    cat("Shared Features (CY & WY):\n"),
    write.table(data.frame(Feature=shared_features), row.names = FALSE, col.names = TRUE, quote = FALSE),
    cat("\n\nFeatures Unique to CY:\n"),
    write.table(data.frame(Feature=cy_only_features), row.names = FALSE, col.names = TRUE, quote = FALSE),
    cat("\n\nFeatures Unique to WY:\n"),
    write.table(data.frame(Feature=wy_only_features), row.names = FALSE, col.names = TRUE, quote = FALSE),
    file = file.path(venn_dir, "venn_feature_lists.txt")
  )
  
  cat("  - 特征列表已保存\n")
  
  return(list(
    plot = venn_list,
    lists = list(shared = shared_features, cy_only = cy_only_features, wy_only = wy_only_features)
  ))
}

# 执行Venn图分析
venn_results <- perform_group_venn_analysis(all_data$integrated)

cat("Venn图分析完成\n\n")

# ==============================================================================
# --- 10. 综合结果汇总 (原步骤8) ---
# ==============================================================================
cat("====================================================================\n")
cat("--- 步骤10: 综合结果汇总 ---\n")
cat("====================================================================\n")

# 创建综合报告函数
create_summary_report <- function(output_dir, alpha_results, beta_results, bar_results, diff_results, lefse_results) {
  
  cat("开始生成综合PDF报告...\n")
  
  summary_file_path <- file.path(output_dir, "Comprehensive_Analysis_Report.md")
  
  # --- 报告标题 ---
  report_content <- c(
    "# 微生物组数据分析综合报告",
    paste("## 分析对象: CY (养殖) vs WY (野生)"),
    paste("报告生成日期:", format(Sys.time(), "%Y-%m-%d %H:%M:%S")),
    "---"
  )
  
  # --- Alpha多样性 ---
  report_content <- c(report_content, "### 1. Alpha多样性分析",
                      "Alpha多样性衡量样本内的物种丰富度和均匀度。",
                      "分析结果显示了CY组和WY组在各分类学水平的多样性差异。",
                      "![Alpha多样性箱线图](plots/alpha_diversity_box.png)")
  
  # --- Beta多样性 ---
  report_content <- c(report_content, "### 2. Beta多样性分析",
                      "Beta多样性衡量样本间的物种组成差异。图中点越近，代表群落结构越相似。",
                      "分析结果显示了CY组和WY组在群落结构上的差异。",
                      "![Beta多样性分析图](plots/integrated_beta_diversity.png)")
  
  # --- 物种组成 ---
  report_content <- c(report_content, "### 3. 物种组成分析",
                      "展示了各分类学水平上前10个物种的组成情况。",
                      "![物种组成堆叠图](plots/integrated_composition_percentage_combined.png)")
  
  # --- Top特征分析 ---
  report_content <- c(report_content, "### 4. Top特征分析",
                      "展示了两组间丰度最高的微生物特征对比。",
                      "![Top特征分析图](plots/integrated_top_features.png)")
  
  # --- 差异丰度分析 ---
  report_content <- c(report_content, "### 5. 差异丰度分析",
                      "火山图展示了在CY和WY组之间具有统计学显著差异的物种。",
                      "![整合差异丰度火山图](plots/integrated_volcano_plot.png)")
  
  # --- LEfSe 分析 ---
  report_content <- c(report_content, "### 6. LEfSe 生物标志物发现",
                      "LEfSe分析用于识别在不同组别中丰度具有统计学和生物学双重显著差异的生物标志物。",
                      "![LEfSe分支图](8_LEfSe_Analysis_New/lefse_cladogram.png)")
  
  # --- Venn图分析 ---
  report_content <- c(report_content, "### 7. Venn图分析",
                      "Venn图展示了CY组和WY组之间特征的重叠情况。",
                      "![Venn图分析](9_Group_Venn_Analysis/venn_CY_vs_WY.png)")
  
  # --- 写入文件 ---
  writeLines(report_content, summary_file_path)
  cat("报告已保存到:", summary_file_path, "\n")
  
  # 提示: 可使用Pandoc将Markdown转为PDF: pandoc Comprehensive_Analysis_Report.md -o report.pdf --pdf-engine=xelatex -V mainfont="SimSun"
}

# 执行报告生成
create_summary_report(
  output_dir = base_output_dir,
  alpha_results = alpha_results,
  beta_results = integrated_beta_results,
  bar_results = composition_results,
  diff_results = diff_results,
  lefse_results = lefse_results
)

cat("综合报告生成完成\n\n")

cat("====================================================================\n")
cat("--- 分析完成! ---\n")
cat("====================================================================\n")
cat("所有结果已保存至:", normalizePath(base_output_dir), "\n")
cat("- 图表目录:", normalizePath(plots_dir), "\n")
cat("- 结果目录:", normalizePath(results_dir), "\n")
cat("- 综合报告:", normalizePath(file.path(base_output_dir, "Comprehensive_Analysis_Report.md")), "\n")

# 列出所有生成的文件
cat("\n生成的图片文件:\n")
plot_files <- list.files(plots_dir, pattern = "\\.(png|pdf)$", full.names = TRUE)
for (file in plot_files) {
  cat("  -", basename(file), "\n")
}

cat("\n生成的结果文件:\n")
result_files <- list.files(results_dir, full.names = TRUE)
for (file in result_files) {
  cat("  -", basename(file), "\n")
}

# 再次打开输出目录
if (.Platform$OS.type == "windows") {
  shell.exec(normalizePath(base_output_dir))
  cat("\n输出目录已在文件资源管理器中打开\n")
}

cat("====================================================================\n")


















