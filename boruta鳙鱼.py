#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Boruta特征选择流程 - Python版本
基于2.R脚本的完全一致实现，使用2/3 CPU加速计算

作者: AI Assistant
日期: 2025-01-27
版本: 1.0 (基于2.R脚本)

主要功能:
1. 完全复制2.R脚本的逻辑和流程
2. 使用2/3 CPU核心进行并行计算加速
3. 快速Boruta特征选择 (无Bootstrap，提升速度)
4. 输出与R脚本完全一致的结果
"""

import pandas as pd
import numpy as np
import os
import logging
from multiprocessing import Pool, cpu_count
import warnings
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import joblib
from functools import partial
import sys
from tqdm import tqdm

# 尝试导入Boruta (如果安装了boruta包)
try:
    from boruta import BorutaPy
    from sklearn.ensemble import RandomForestClassifier
    BORUTA_AVAILABLE = True
except ImportError:
    BORUTA_AVAILABLE = False
    print("警告: boruta包未安装，将使用替代实现")

# 设置警告和日志
warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BorutaPipelinePython:
    """
    Boruta特征选择流程 - Python实现
    完全基于2.R脚本的逻辑
    """
    
    def __init__(self):
        """初始化参数 (与2.R脚本完全一致)"""
        
        # --- 1.1 输入文件配置 ---
        self.input_files = {
            'phylum': r'C:\Users\<USER>\Desktop\特征提取\Y门.txt',
            'class': r'C:\Users\<USER>\Desktop\特征提取\Y纲.txt',
            'order': r'C:\Users\<USER>\Desktop\特征提取\Y目.txt',
            'family': r'C:\Users\<USER>\Desktop\特征提取\Y科.txt',
            'genus': r'C:\Users\<USER>\Desktop\特征提取\Y属.txt',
            'species': r'C:\Users\<USER>\Desktop\特征提取\Y种.txt'
        }
        
        # --- 1.2 输出目录配置 ---
        self.base_output_dir = "C:/Users/<USER>/Desktop/Boruta_Pipeline_V2_Output"
        self.transformed_data_dir = os.path.join(self.base_output_dir, "1_Transformed_Per_Level")
        self.elite_features_dir = os.path.join(self.base_output_dir, "2_Elite_Features_Data")
        self.per_level_analysis_dir = os.path.join(self.base_output_dir, "3_Analysis_Per_Level")
        self.final_plots_dir = os.path.join(self.base_output_dir, "4_Final_Combined_Plots")
        
        # --- 1.3 数据划分参数 ---
        self.sample_id_col = "SampleID"
        self.group_col = "Group"
        self.train_ratio = 0.8
        self.prevalence_threshold = 0.01
        self.abundance_threshold = 0.0001
        self.random_state = 42
        
        # --- 1.4 Boruta参数配置 ---
        self.boruta_max_runs = 500
        self.boruta_p_value = 0.01
        self.boruta_mc_adj = True
        self.boruta_do_trace = 1
        # 移除Bootstrap相关参数
        
        # --- 1.5 特征质量过滤参数 ---
        self.variance_threshold = 0.01
        
        # CPU配置 (使用2/3核心)
        self.n_jobs = max(1, int(cpu_count() * 2 / 3))
        logger.info(f"将使用 {self.n_jobs}/{cpu_count()} 个CPU核心进行并行计算")
        
        # 初始化结果收集器
        self.all_levels_top_features_df = pd.DataFrame()
        self.elite_features_all_levels = []
        
        # 设置随机种子
        np.random.seed(self.random_state)
        
    def create_output_directories(self):
        """创建输出目录"""
        directories = [
            self.base_output_dir,
            self.transformed_data_dir,
            self.elite_features_dir,
            self.per_level_analysis_dir,
            self.final_plots_dir
        ]
        
        # 为每个分类水平创建独立文件夹
        for level_name in self.input_files.keys():
            level_dir = os.path.join(self.elite_features_dir, level_name)
            directories.append(level_dir)
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            
        logger.info("输出目录创建完成（包含各分类水平独立文件夹）")
    
    def get_short_feature_name(self, full_name):
        """从完整的分类学名称中提取最后一级分类"""
        if not full_name or full_name == "":
            return ""
        
        parts = full_name.split(";")
        if len(parts) == 1:
            return full_name
        return parts[-1]
    
    def near_zero_var_filter(self, X_df, freq_cut=15, unique_cut=5):
        """
        Python implementation of R's caret::nearZeroVar.
        Identifies and returns a boolean mask for features to KEEP.

        参数 (基于PMC 2024文献推荐):
        - freq_cut: 15 (频率比阈值，适合微生物组零膨胀特性)
        - unique_cut: 5 (独特值百分比阈值，适合高维稀疏数据)

        返回:
        - pd.Series: 布尔掩码，True表示保留的特征
        """
        logger.info("Applying near-zero variance filter (caret::nearZeroVar logic)...")
        logger.info(f"参数设置: freq_cut={freq_cut}, unique_cut={unique_cut} (基于文献推荐)")
        
        # Calculate statistics for each column
        unique_counts = X_df.nunique()
        
        # Rule 1: Zero variance (only one unique value)
        zero_var_cols = unique_counts == 1
        
        # Rule 2: Near-zero variance
        nzv_cols = []
        for col in X_df.columns:
            if not zero_var_cols[col]:
                # Calculate unique value percentage
                unique_percent = (unique_counts[col] / len(X_df)) * 100
                
                # Calculate frequency ratio
                counts = X_df[col].value_counts()
                if len(counts) > 1:
                    freq_ratio = counts.iloc[0] / counts.iloc[1]
                else:
                    freq_ratio = 1
                
                if unique_percent < unique_cut and freq_ratio > freq_cut:
                    nzv_cols.append(col)
        
        cols_to_remove = set(X_df.columns[zero_var_cols]) | set(nzv_cols)
        
        logger.info(f"nearZeroVar logic identified {len(cols_to_remove)} features to remove.")
        
        # Return a boolean Series of features to KEEP
        keep_mask = ~X_df.columns.isin(cols_to_remove)
        return pd.Series(keep_mask, index=X_df.columns)
    
    def enhanced_feature_quality_filter(self, data, group_col):
        """
        增强的特征质量过滤 (基于文献最佳实践)
        注意: 相关性过滤已移至Python LGBM-RFE阶段，避免重复处理
        """
        logger.info("开始增强特征质量过滤 (不包含相关性过滤)...")
        
        # 获取特征列 (排除样本ID和分组列)
        feature_cols = [col for col in data.columns if col not in [self.sample_id_col, group_col]]
        feature_data = data[feature_cols]
        
        original_features = len(feature_cols)
        logger.info(f"原始特征数: {original_features}")
        
        # 1. 流行度过滤 - 在至少1%样本中出现
        prevalence_filter = (feature_data > 0).sum() / len(feature_data) >= self.prevalence_threshold
        features_after_prevalence = prevalence_filter.sum()
        logger.info(f"流行度过滤后特征数: {features_after_prevalence} "
                   f"(移除 {original_features - features_after_prevalence} 个)")
        
        # 2. 极低丰度过滤
        abundance_filter = feature_data.mean() >= self.abundance_threshold
        
        # 3. 方差过滤 (使用与R脚本一致的nearZeroVar逻辑)
        variance_filter = self.near_zero_var_filter(feature_data)
        removed_by_variance = (~variance_filter).sum()
        logger.info(f"方差过滤 (nearZeroVar logic) 移除了 {removed_by_variance} 个特征")
        
        # 4. PCR友好过滤 - 移除未分类、未培养、包含数字的特征
        pcr_friendly_filter = ~feature_data.columns.str.contains(
            'unclassified|uncultured|unknown|[0-9]', case=False, regex=True
        )
        pcr_friendly_filter = pd.Series(pcr_friendly_filter, index=feature_data.columns)
        
        # 最终过滤条件 (不包含相关性过滤)
        final_filter = prevalence_filter & abundance_filter & variance_filter & pcr_friendly_filter
        features_after_all = final_filter.sum()
        filter_efficiency = (1 - features_after_all/original_features) * 100
        
        logger.info(f"所有过滤后特征数: {features_after_all} "
                   f"(总过滤效率: {filter_efficiency:.2f}%)")
        logger.info(f"注意: 相关性过滤将在Python LGBM-RFE阶段进行")
        
        # 应用过滤
        filtered_features = feature_data.columns[final_filter]
        filtered_data = data[[self.sample_id_col, group_col] + filtered_features.tolist()]
        
        return {
            'data': filtered_data,
            'features': filtered_features.tolist(),
            'filter_stats': {
                'original': original_features,
                'after_prevalence': features_after_prevalence,
                'after_abundance': (prevalence_filter & abundance_filter).sum(),
                'after_variance': (prevalence_filter & abundance_filter & variance_filter).sum(),
                'after_pcr': features_after_all,
                'final': len(filtered_features)
            }
        }
    
    def bootstrap_stability_assessment_single(self, args):
        """单次Bootstrap评估 (用于并行计算)"""
        from sklearn.ensemble import RandomForestClassifier
        data, group_col, i, boruta_params = args
        
        try:
            # Bootstrap采样
            boot_indices = np.random.choice(len(data), size=len(data), replace=True)
            boot_data = data.iloc[boot_indices].reset_index(drop=True)
            
            # 准备Boruta输入
            feature_cols = [col for col in boot_data.columns if col not in [self.sample_id_col, group_col]]
            X_boot = boot_data[feature_cols].values
            y_boot = boot_data[group_col].values
            
            # 运行Boruta
            if BORUTA_AVAILABLE:
                # 使用boruta包
                # 基于PMC 2023文献优化的随机森林参数
                rf = RandomForestClassifier(
                    n_estimators=500,        # 文献推荐: 500-1000棵树
                    max_depth=None,          # 文献推荐: 不限制深度，让树充分生长
                    min_samples_split=2,     # 文献推荐: 默认值2
                    min_samples_leaf=1,      # 文献推荐: 默认值1
                    max_features='sqrt',     # 文献推荐: sqrt(n_features)
                    bootstrap=True,          # 文献推荐: 启用Bootstrap
                    random_state=self.random_state + i,
                    n_jobs=1  # 单个进程内不再并行
                )
                
                boruta_selector = BorutaPy(
                    rf,
                    n_estimators='auto',
                    max_iter=boruta_params.get('maxRuns', 100),
                    alpha=boruta_params.get('pValue', 0.01),
                    verbose=0,
                    random_state=self.random_state + i
                )
                
                boruta_selector.fit(X_boot, y_boot)
                selected_features = [feature_cols[j] for j in range(len(feature_cols)) 
                                   if boruta_selector.support_[j]]
            else:
                # 使用随机森林重要性作为替代 (基于文献优化参数)
                rf = RandomForestClassifier(
                    n_estimators=500,        # 文献推荐: 500-1000棵树
                    max_depth=None,          # 文献推荐: 不限制深度
                    min_samples_split=2,     # 文献推荐: 默认值
                    min_samples_leaf=1,      # 文献推荐: 默认值
                    max_features='sqrt',     # 文献推荐: sqrt(n_features)
                    bootstrap=True,          # 文献推荐: 启用Bootstrap
                    random_state=self.random_state + i,
                    n_jobs=1
                )
                rf.fit(X_boot, y_boot)
                
                # 选择重要性前50%的特征作为替代
                importances = rf.feature_importances_
                threshold = np.percentile(importances, 50)
                selected_features = [feature_cols[j] for j in range(len(feature_cols)) 
                                   if importances[j] >= threshold]
            
            return selected_features
            
        except Exception as e:
            logger.warning(f"Bootstrap {i} 失败: {e}")
            return []
    
    def bootstrap_stability_assessment(self, data, group_col, n_bootstrap=1000):
        """Bootstrap稳定性评估 (基于文献推荐，并行计算)"""
        logger.info(f"开始Bootstrap稳定性评估 ({n_bootstrap} 次采样，使用 {self.n_jobs} 个CPU核心)...")
        
        # 准备参数
        boruta_params = {
            'maxRuns': self.boruta_max_runs,
            'pValue': self.boruta_p_value,
            'mcAdj': self.boruta_mc_adj
        }
        
        # 准备并行参数
        args_list = [(data, group_col, i, boruta_params) for i in range(n_bootstrap)]
        
        # 并行执行Bootstrap，并使用tqdm显示进度条
        selected_features_list = []
        with Pool(processes=self.n_jobs) as pool:
            # 使用imap_unordered并配合tqdm显示进度条
            # imap_unordered可以实时返回结果，而不会像map一样等待所有任务完成
            pbar = tqdm(total=n_bootstrap, desc=f"Bootstrap评估", file=sys.stdout, bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]')
            for result in pool.imap_unordered(self.bootstrap_stability_assessment_single, args_list):
                if result:  # 确保结果不为空
                    selected_features_list.append(result)
                pbar.update(1)
            pbar.close()
        
        # 计算特征选择频率
        all_features = list(set([feature for sublist in selected_features_list for feature in sublist]))
        feature_frequency = {}
        
        for feature in all_features:
            count = sum(1 for feature_list in selected_features_list if feature in feature_list)
            feature_frequency[feature] = count / n_bootstrap
        
        # 按频率排序
        sorted_features = sorted(feature_frequency.items(), key=lambda x: x[1], reverse=True)

        logger.info("Bootstrap稳定性评估完成")
        logger.info("特征选择频率统计:")
        frequencies = list(feature_frequency.values())
        if frequencies:
            logger.info(f"  最小值: {min(frequencies):.3f}")
            logger.info(f"  最大值: {max(frequencies):.3f}")
            logger.info(f"  平均值: {np.mean(frequencies):.3f}")
            logger.info(f"  中位数: {np.median(frequencies):.3f}")

        # 显示频率最高的前10个特征
        logger.info("频率最高的前10个特征:")
        for i, (feature, freq) in enumerate(sorted_features[:10], 1):
            logger.info(f"  {i}. {feature}: {freq:.3f}")
        
        return {
            'feature_frequency': feature_frequency,
            'selected_features_list': selected_features_list,
            'stable_features': [feature for feature, freq in feature_frequency.items() if freq >= 0.5]
        }

    def process_single_level(self, level_name, file_path):
        """处理单个分类水平 (与2.R脚本逻辑完全一致)"""
        logger.info(f"=== 开始处理分类水平: {level_name} ===")

        try:
            # 步骤1: 加载数据
            logger.info(f"步骤1: 加载数据文件: {file_path}")
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return None

            data = pd.read_csv(file_path, sep='\t')
            logger.info(f"数据维度: {data.shape}")

            # 检查必要列
            if self.sample_id_col not in data.columns or self.group_col not in data.columns:
                logger.error(f"缺少必要列: {self.sample_id_col} 或 {self.group_col}")
                return None

            # 步骤2: 全局数据划分 (训练集/验证集) - 保持类别平衡并按类别排列
            logger.info("步骤2: 全局数据划分 (训练集/验证集)")

            # 分层抽样保持类别平衡
            train_data, val_data = train_test_split(
                data,
                test_size=1-self.train_ratio,
                stratify=data[self.group_col],
                random_state=self.random_state
            )

            # 按类别排列数据 - 训练集和验证集都按Group列排序
            train_data = train_data.sort_values(by=[self.group_col, self.sample_id_col]).reset_index(drop=True)
            val_data = val_data.sort_values(by=[self.group_col, self.sample_id_col]).reset_index(drop=True)

            # 详细记录类别分布
            train_class_counts = train_data[self.group_col].value_counts().sort_index()
            val_class_counts = val_data[self.group_col].value_counts().sort_index()
            
            logger.info(f"全局数据划分完成 (按类别排列):")
            logger.info(f" - 训练集样本数: {len(train_data)}")
            logger.info(f" - 验证集样本数: {len(val_data)}")
            logger.info(f" - 训练集类别分布: {dict(train_class_counts)}")
            logger.info(f" - 验证集类别分布: {dict(val_class_counts)}")
            
            # 验证类别平衡性
            for class_name in train_class_counts.index:
                train_ratio_actual = train_class_counts[class_name] / len(train_data)
                val_ratio_actual = val_class_counts[class_name] / len(val_data)
                logger.info(f" - 类别 '{class_name}' 比例: 训练集 {train_ratio_actual:.3f}, 验证集 {val_ratio_actual:.3f}")

            # 步骤3: 特征质量过滤 (仅基于训练集)
            logger.info("步骤3: 特征质量过滤 (仅基于训练集)")
            logger.info("注意: 所有特征筛选步骤(流行度、丰度、方差、PCR友好性)都仅基于训练集数据")
            filter_result = self.enhanced_feature_quality_filter(train_data, self.group_col)
            train_data_filtered = filter_result['data']

            logger.info(f"过滤后训练集维度: {train_data_filtered.shape}")
            logger.info(f"特征筛选统计 (仅基于训练集): {filter_result['filter_stats']}")

            # 步骤4: 直接在训练集上运行Boruta (确保只基于训练集)
            logger.info("步骤4: 直接在训练集上运行Boruta")
            logger.info("重要: Boruta特征选择仅基于训练集数据，确保无数据泄露")

            # 准备Boruta输入数据
            feature_cols = [col for col in train_data_filtered.columns
                           if col not in [self.sample_id_col, self.group_col]]

            logger.info(f"Boruta输入数据维度: {len(train_data_filtered)} 样本 × {len(feature_cols)} 特征")
            logger.info(f"训练集类别分布: {dict(train_data_filtered[self.group_col].value_counts().sort_index())}")

            # 运行Boruta 
            if BORUTA_AVAILABLE:
                X_train = train_data_filtered[feature_cols].values
                y_train = train_data_filtered[self.group_col].values

                # 编码标签
                le = LabelEncoder()
                y_train_encoded = le.fit_transform(y_train)

                logger.info(f"开始Boruta分析 (maxRuns = {self.boruta_max_runs}, pValue = {self.boruta_p_value})...")

                # 基于PMC 2023文献优化的随机森林参数
                rf = RandomForestClassifier(
                    n_estimators=500,        # 文献推荐: 500-1000棵树提高稳定性
                    max_depth=None,          # 文献推荐: 不限制深度，让树充分生长
                    min_samples_split=2,     # 文献推荐: 默认值2
                    min_samples_leaf=1,      # 文献推荐: 默认值1
                    max_features='sqrt',     # 文献推荐: sqrt(n_features)
                    bootstrap=True,          # 文献推荐: 启用Bootstrap采样
                    random_state=self.random_state,
                    n_jobs=self.n_jobs
                )

                boruta_selector = BorutaPy(
                    rf,
                    n_estimators='auto',
                    max_iter=self.boruta_max_runs,
                    alpha=self.boruta_p_value,
                    verbose=self.boruta_do_trace,
                    random_state=self.random_state
                )

                boruta_selector.fit(X_train, y_train_encoded)
                confirmed_features = [feature_cols[i] for i in range(len(feature_cols))
                                    if boruta_selector.support_[i]]
                tentative_features = [feature_cols[i] for i in range(len(feature_cols))
                                    if boruta_selector.support_weak_[i]]
                
                # 收集Boruta详细统计信息
                boruta_feature_stats = self.extract_boruta_feature_statistics(
                    boruta_selector, feature_cols, train_data_filtered, self.group_col
                )
                
                # 对p值进行多重比较校正
                boruta_feature_stats = self.apply_multiple_comparison_correction(boruta_feature_stats)
            else:
                # 使用随机森林重要性作为替代
                logger.info("使用随机森林重要性作为Boruta替代方法...")
                X_train = train_data_filtered[feature_cols].values
                y_train = train_data_filtered[self.group_col].values

                le = LabelEncoder()
                y_train_encoded = le.fit_transform(y_train)

                # 基于PMC 2023文献优化的随机森林参数 (替代Boruta)
                rf = RandomForestClassifier(
                    n_estimators=500,        # 文献推荐: 500-1000棵树
                    max_depth=None,          # 文献推荐: 不限制深度
                    min_samples_split=2,     # 文献推荐: 默认值
                    min_samples_leaf=1,      # 文献推荐: 默认值
                    max_features='sqrt',     # 文献推荐: sqrt(n_features)
                    bootstrap=True,          # 文献推荐: 启用Bootstrap
                    random_state=self.random_state,
                    n_jobs=self.n_jobs
                )
                rf.fit(X_train, y_train_encoded)

                # 选择重要性前30%的特征作为确认特征
                importances = rf.feature_importances_
                threshold_confirmed = np.percentile(importances, 70)
                threshold_tentative = np.percentile(importances, 50)

                confirmed_features = [feature_cols[i] for i in range(len(feature_cols))
                                    if importances[i] >= threshold_confirmed]
                tentative_features = [feature_cols[i] for i in range(len(feature_cols))
                                    if threshold_tentative <= importances[i] < threshold_confirmed]
                
                # 收集随机森林特征统计信息（替代Boruta）
                boruta_feature_stats = self.extract_rf_feature_statistics(
                    rf, feature_cols, importances, train_data_filtered, self.group_col
                )
                
                # 对p值进行多重比较校正
                boruta_feature_stats = self.apply_multiple_comparison_correction(boruta_feature_stats)

            logger.info(f"Boruta结果:")
            logger.info(f"  - 确认特征数: {len(confirmed_features)}")
            logger.info(f"  - 包含待定特征数: {len(tentative_features)}")

            # 步骤5: 使用双重排序法选择前10个显著且重要的特征
            # 首先从Boruta确认特征中筛选显著特征，然后按重要性排序选择前10个
            
            # 5.1 从特征统计中提取显著性和重要性信息
            significant_features_with_stats = []
            for feature in confirmed_features:
                # 在统计信息中查找该特征的详细信息
                for stat in boruta_feature_stats:
                    if stat.get('feature_name') == feature and stat.get('significant', False):
                        significant_features_with_stats.append({
                            'feature_name': feature,
                            'importance_score': stat.get('importance_score', 0),
                            'p_value_adj': stat.get('p_value_adj', 1.0),
                            'stats': stat
                        })
                        break
            
            logger.info(f"显著性过滤结果:")
            logger.info(f"  - Boruta确认特征: {len(confirmed_features)}")
            logger.info(f"  - 差异显著特征: {len(significant_features_with_stats)}")
            logger.info(f"  - 过滤掉特征: {len(confirmed_features) - len(significant_features_with_stats)}")
            
            # 5.2 按重要性得分排序（降序）
            significant_features_with_stats.sort(key=lambda x: x['importance_score'], reverse=True)
            
            # 5.3 选择前10个特征（如果显著特征少于10个，则全部选择）
            max_features = 10
            top_features_count = min(max_features, len(significant_features_with_stats))
            top_features_data = significant_features_with_stats[:top_features_count]
            final_features = [item['feature_name'] for item in top_features_data]
            
            logger.info(f"双重排序特征选择结果:")
            logger.info(f"  - 目标特征数: {max_features}")
            logger.info(f"  - 实际选择数: {top_features_count}")
            logger.info(f"  - 选择策略: 显著性过滤 + 重要性排序")
            
            # 5.4 显示选择的前10个特征详细信息
            logger.info("最终选择的特征（按重要性排序）:")
            for i, item in enumerate(top_features_data, 1):
                short_name = self.get_short_feature_name(item['feature_name'])
                logger.info(f"  {i}. {short_name} (重要性: {item['importance_score']:.4f}, "
                           f"校正p值: {item['p_value_adj']:.4e})")
            
            # 检查是否有最终特征
            if not final_features:
                logger.warning(f"分类水平 {level_name} 没有显著差异的特征，跳过保存")
                return None

            # 步骤6: 保存结果 (验证集严格使用训练集筛选的特征)
            logger.info("步骤6: 保存处理结果...")
            logger.info("注意: 验证集将严格使用训练集Boruta筛选出的特征，不进行独立筛选")

            # 构建最终特征列表 (基于训练集筛选结果)
            final_columns = [self.sample_id_col, self.group_col] + final_features

            # 确保验证集包含所有特征 (这应该总是成立，因为特征来自原始数据)
            missing_in_val = [col for col in final_columns if col not in val_data.columns]
            if missing_in_val:
                logger.warning(f"验证集中缺少特征: {missing_in_val}")
                logger.warning("这表明数据预处理可能存在问题")
                # 移除缺失的特征
                final_columns = [col for col in final_columns if col in val_data.columns]
                final_features = [f for f in final_features if f in val_data.columns]
            else:
                logger.info("✅ 验证集包含所有训练集筛选的特征")

            # 构建最终数据集
            final_train_data = train_data_filtered[final_columns].copy()
            final_val_data = val_data[final_columns].copy()  # 直接使用原始验证集数据，应用训练集筛选的特征
            
            logger.info(f"最终数据集构建完成:")
            logger.info(f" - 训练集: {final_train_data.shape} (来源: 筛选后的训练集)")
            logger.info(f" - 验证集: {final_val_data.shape} (来源: 原始验证集，应用训练集特征筛选)")
            
            # 将特征名转换为简称（保留SampleID和Group）
            feature_name_mapping = {}
            for feature in final_features:
                short_name = self.get_short_feature_name(feature)
                feature_name_mapping[feature] = short_name
            
            # 重命名特征列
            final_train_data.rename(columns=feature_name_mapping, inplace=True)
            final_val_data.rename(columns=feature_name_mapping, inplace=True)

            # 保存到对应分类水平文件夹
            level_dir = os.path.join(self.elite_features_dir, level_name)
            train_file = os.path.join(level_dir, f"{level_name}_elite_features_TRAIN_raw.csv")
            val_file = os.path.join(level_dir, f"{level_name}_elite_features_VALIDATION_raw.csv")

            final_train_data.to_csv(train_file, index=False)
            final_val_data.to_csv(val_file, index=False)

            logger.info(f"结果已保存:")
            logger.info(f"  - 训练集: {train_file}")
            logger.info(f"  - 验证集: {val_file}")
            logger.info(f"  - 最终特征数: {len(final_features)}")

            return {
                'level': level_name,
                'features': final_features,
                'train_file': train_file,
                'val_file': val_file,
                'train_shape': final_train_data.shape,
                'val_shape': final_val_data.shape,
                'filter_stats': filter_result['filter_stats'],
                'feature_stats': boruta_feature_stats
            }

        except Exception as e:
            logger.error(f"处理 {level_name} 时发生错误: {e}")
            return None

    def run_pipeline(self):
        """运行完整的Boruta流程"""
        logger.info("=== 增强版微生物特征选择流程启动 (Python版本) ===")
        logger.info(f"输出目录: {self.base_output_dir}")
        logger.info(f"训练集比例: {self.train_ratio}")
        logger.info(f"流行度阈值: {self.prevalence_threshold}")
        logger.info(f"丰度阈值: {self.abundance_threshold}")
        logger.info(f"CPU核心数: {self.n_jobs}/{cpu_count()}")

        # 创建输出目录
        self.create_output_directories()

        # 处理所有分类水平
        results = []
        for level_name, file_path in self.input_files.items():
            logger.info(f"\n--- 开始处理分类水平: {level_name} ---")
            result = self.process_single_level(level_name, file_path)
            if result:
                results.append(result)
            else:
                logger.warning(f"分类水平 {level_name} 处理失败或无有效特征，已跳过")

        # 汇总结果
        logger.info("\n=== 所有分类水平处理完成 ===")
        logger.info("处理结果汇总:")

        total_features = 0
        for result in results:
            logger.info(f"  {result['level']}: {len(result['features'])} 个特征")
            total_features += len(result['features'])

        logger.info(f"总特征数: {total_features}")

        # 生成整合数据集和统计报告
        logger.info("\n=== 生成整合数据集和统计报告 ===")
        self.create_integrated_datasets(results)
        self.generate_feature_statistics_report(results)

        logger.info("✅ 所有分类水平处理完成")

        return results

    def create_integrated_datasets(self, results):
        """创建整合的训练集和验证集"""
        logger.info("开始创建整合数据集...")
        
        if not results:
            logger.warning("没有处理结果，无法创建整合数据集")
            return
        
        # 收集所有特征
        all_features = []
        level_feature_mapping = {}
        
        for result in results:
            level_name = result['level']
            features = result['features']
            all_features.extend(features)
            level_feature_mapping[level_name] = features
            logger.info(f"  {level_name}: {len(features)} 个特征")
        
        logger.info(f"整合特征总数: {len(all_features)}")
        
        # 加载第一个数据集作为基础（包含SampleID和Group）
        first_result = results[0]
        base_train_data = pd.read_csv(first_result['train_file'])
        base_val_data = pd.read_csv(first_result['val_file'])
        
        # 创建整合的训练集和验证集
        integrated_train_data = base_train_data[['SampleID', 'Group']].copy()
        integrated_val_data = base_val_data[['SampleID', 'Group']].copy()
        
        # 逐个分类水平添加特征
        for result in results:
            level_name = result['level']
            train_data = pd.read_csv(result['train_file'])
            val_data = pd.read_csv(result['val_file'])
            
            # 获取该水平的特征列
            level_features = [col for col in train_data.columns 
                            if col not in ['SampleID', 'Group']]
            
            if level_features:
                # 添加特征到整合数据集（直接使用简称，不添加前缀）
                for feature in level_features:
                    if feature in train_data.columns and feature in val_data.columns:
                        # 检查是否已存在同名特征，如果存在则添加分类水平后缀避免冲突
                        final_feature_name = feature
                        if feature in integrated_train_data.columns:
                            final_feature_name = f"{feature}_{level_name}"
                            logger.warning(f"特征名冲突，已重命名: {feature} -> {final_feature_name}")
                        
                        integrated_train_data[final_feature_name] = train_data[feature]
                        integrated_val_data[final_feature_name] = val_data[feature]
        
        # 保存整合数据集
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        integrated_train_file = os.path.join(
            self.elite_features_dir, 
            f"INTEGRATED_ALL_LEVELS_TRAIN_{timestamp}.csv"
        )
        integrated_val_file = os.path.join(
            self.elite_features_dir, 
            f"INTEGRATED_ALL_LEVELS_VALIDATION_{timestamp}.csv"
        )
        
        integrated_train_data.to_csv(integrated_train_file, index=False)
        integrated_val_data.to_csv(integrated_val_file, index=False)
        
        # 同时保存标准命名版本
        standard_train_file = os.path.join(self.elite_features_dir, "INTEGRATED_FEATURES_TRAIN.csv")
        standard_val_file = os.path.join(self.elite_features_dir, "INTEGRATED_FEATURES_VALIDATION.csv")
        
        integrated_train_data.to_csv(standard_train_file, index=False)
        integrated_val_data.to_csv(standard_val_file, index=False)
        
        logger.info("整合数据集创建完成:")
        logger.info(f"  整合训练集: {integrated_train_file}")
        logger.info(f"  整合验证集: {integrated_val_file}")
        logger.info(f"  标准训练集: {standard_train_file}")
        logger.info(f"  标准验证集: {standard_val_file}")
        logger.info(f"  整合后特征数: {len(integrated_train_data.columns) - 2}")  # 减去SampleID和Group
        logger.info(f"  训练样本数: {len(integrated_train_data)}")
        logger.info(f"  验证样本数: {len(integrated_val_data)}")
        
        return {
            'train_file': integrated_train_file,
            'val_file': integrated_val_file,
            'standard_train_file': standard_train_file,
            'standard_val_file': standard_val_file,
            'total_features': len(integrated_train_data.columns) - 2,
            'train_samples': len(integrated_train_data),
            'val_samples': len(integrated_val_data),
            'level_feature_mapping': level_feature_mapping
        }
    
    def generate_feature_statistics_report(self, results):
        """生成详细的特征统计报告"""
        logger.info("开始生成特征统计报告...")
        
        if not results:
            logger.warning("没有处理结果，无法生成统计报告")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 1. 创建总体统计报告
        overall_stats = {
            'pipeline_version': '1.0',
            'execution_time': timestamp,
            'total_levels_processed': len(results),
            'train_ratio': self.train_ratio,
            'prevalence_threshold': self.prevalence_threshold,
            'abundance_threshold': self.abundance_threshold,
            'boruta_max_runs': self.boruta_max_runs,
            'boruta_p_value': self.boruta_p_value,
            'cpu_cores_used': self.n_jobs,
            'total_cpu_cores': cpu_count()
        }
        
        # 2. 创建每个分类水平的详细统计
        level_stats = []
        total_original_features = 0
        total_selected_features = 0
        
        for result in results:
            level_name = result['level']
            features = result['features']
            filter_stats = result.get('filter_stats', {})
            
            level_stat = {
                'level': level_name,
                'selected_features_count': len(features),
                'train_samples': result['train_shape'][0],
                'val_samples': result['val_shape'][0],
                'filter_efficiency': 0,
                'selected_features': features
            }
            
            # 添加过滤统计信息
            if filter_stats:
                original_count = filter_stats.get('original', 0)
                final_count = filter_stats.get('final', len(features))
                
                level_stat.update({
                    'original_features': original_count,
                    'after_prevalence': filter_stats.get('after_prevalence', 0),
                    'after_abundance': filter_stats.get('after_abundance', 0),
                    'after_variance': filter_stats.get('after_variance', 0),
                    'after_pcr': filter_stats.get('after_pcr', 0),
                    'final_features': final_count,
                    'filter_efficiency': round((1 - final_count/original_count) * 100, 2) if original_count > 0 else 0
                })
                
                total_original_features += original_count
                total_selected_features += len(features)
            
            level_stats.append(level_stat)
        
        # 3. 计算总体效率
        overall_filter_efficiency = round((1 - total_selected_features/total_original_features) * 100, 2) if total_original_features > 0 else 0
        overall_stats.update({
            'total_original_features': total_original_features,
            'total_selected_features': total_selected_features,
            'overall_filter_efficiency': overall_filter_efficiency
        })
        
        # 4. 保存统计报告
        import json
        
        # 保存JSON格式的详细报告
        detailed_report = {
            'overall_statistics': overall_stats,
            'level_statistics': level_stats,
            'feature_selection_summary': {
                'levels_processed': [stat['level'] for stat in level_stats],
                'features_per_level': {stat['level']: stat['selected_features_count'] for stat in level_stats},
                'total_features': sum(stat['selected_features_count'] for stat in level_stats)
            }
        }
        
        # 转换所有numpy类型为Python原生类型
        detailed_report = self.convert_numpy_types(detailed_report)
        
        json_report_file = os.path.join(
            self.elite_features_dir,
            f"boruta_feature_statistics_report_{timestamp}.json"
        )
        
        with open(json_report_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_report, f, indent=2, ensure_ascii=False)
        
        # 5. 保存CSV格式的汇总表
        summary_df = pd.DataFrame(level_stats)
        csv_report_file = os.path.join(
            self.elite_features_dir,
            f"boruta_feature_summary_{timestamp}.csv"
        )
        summary_df.to_csv(csv_report_file, index=False)
        
        # 6. 生成特征列表文件
        feature_list_data = []
        for result in results:
            level_name = result['level']
            for i, feature in enumerate(result['features'], 1):
                short_name = self.get_short_feature_name(feature)
                feature_list_data.append({
                    'taxonomic_level': level_name,
                    'feature_rank': i,
                    'feature_short_name': short_name
                })
        
        feature_list_df = pd.DataFrame(feature_list_data)
        feature_list_file = os.path.join(
            self.elite_features_dir,
            f"selected_features_list_{timestamp}.csv"
        )
        feature_list_df.to_csv(feature_list_file, index=False)
        
        # 7. 生成特征详细统计文件
        feature_detailed_stats = []
        for result in results:
            if 'feature_stats' in result and result['feature_stats']:
                level_name = result['level']
                for stat in result['feature_stats']:
                    # 只保留确认的特征
                    if stat.get('boruta_confirmed', False):
                        # 只保留核心统计字段，使用简称
                        core_stat = {
                            'taxonomic_level': level_name,
                            'feature_short_name': stat.get('short_name', ''),
                            'importance_score': stat.get('importance_score', 0),
                            'mean': stat.get('mean', 0),
                            'std': stat.get('std', 0),
                            'prevalence': stat.get('prevalence', 0),
                            'fold_change': stat.get('fold_change', 1),
                            'p_value_adj': stat.get('p_value_adj', 1),
                            'significant': stat.get('significant', False)
                        }
                        feature_detailed_stats.append(core_stat)
        
        if feature_detailed_stats:
            detailed_stats_df = pd.DataFrame(feature_detailed_stats)
            detailed_stats_file = os.path.join(
                self.elite_features_dir,
                f"feature_detailed_statistics_{timestamp}.csv"
            )
            detailed_stats_df.to_csv(detailed_stats_file, index=False)
            
            # 保存标准版本
            standard_detailed_stats_file = os.path.join(
                self.elite_features_dir, 
                "feature_detailed_statistics.csv"
            )
            detailed_stats_df.to_csv(standard_detailed_stats_file, index=False)
            
            logger.info(f"特征详细统计文件: {detailed_stats_file}")
            logger.info(f"包含 {len(detailed_stats_df)} 个特征的详细统计信息")
        
        # 8. 保存标准版本
        standard_json_file = os.path.join(self.elite_features_dir, "boruta_statistics_report.json")
        standard_csv_file = os.path.join(self.elite_features_dir, "boruta_feature_summary.csv")
        standard_features_file = os.path.join(self.elite_features_dir, "selected_features_list.csv")
        
        with open(standard_json_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_report, f, indent=2, ensure_ascii=False)
        summary_df.to_csv(standard_csv_file, index=False)
        feature_list_df.to_csv(standard_features_file, index=False)
        
        logger.info("特征统计报告生成完成:")
        logger.info(f"  详细JSON报告: {json_report_file}")
        logger.info(f"  汇总CSV报告: {csv_report_file}")
        logger.info(f"  特征列表文件: {feature_list_file}")
        if feature_detailed_stats:
            logger.info(f"  特征详细统计: {detailed_stats_file}")
        logger.info(f"  标准版本已保存到: {self.elite_features_dir}")
        
        # 9. 输出关键统计信息到日志
        logger.info("\n=== 特征选择统计摘要 ===")
        logger.info(f"总体过滤效率: {overall_filter_efficiency}%")
        logger.info(f"原始特征总数: {total_original_features}")
        logger.info(f"最终特征总数: {total_selected_features}")
        logger.info("各分类水平统计:")
        for stat in level_stats:
            logger.info(f"  {stat['level']}: {stat['selected_features_count']} 特征 "
                       f"(过滤效率: {stat.get('filter_efficiency', 0)}%)")
        
        return detailed_report

    def extract_boruta_feature_statistics(self, boruta_selector, feature_cols, train_data, group_col):
        """提取Boruta特征选择的详细统计信息"""
        logger.info("提取Boruta特征详细统计信息...")
        
        feature_stats = []
        
        # 获取特征数据和分组信息
        X_data = train_data[[col for col in feature_cols if col in train_data.columns]]
        y_data = train_data[group_col]
        groups = y_data.unique()
        
        for i, feature_name in enumerate(feature_cols):
            if feature_name not in train_data.columns:
                continue
                
            feature_data = train_data[feature_name]
            
            # Boruta相关统计
            is_confirmed = boruta_selector.support_[i] if i < len(boruta_selector.support_) else False
            is_tentative = boruta_selector.support_weak_[i] if i < len(boruta_selector.support_weak_) else False
            
            # 重要性得分（如果可用）
            importance_score = 0
            z_score = 0
            if hasattr(boruta_selector, 'ranking_'):
                ranking = boruta_selector.ranking_[i] if i < len(boruta_selector.ranking_) else 999
            else:
                ranking = 999
                
            # 基本分布统计
            basic_stats = self.calculate_feature_distribution_stats(feature_data, y_data, groups)
            
            # 分类学信息
            short_name = self.get_short_feature_name(feature_name)
            
            feature_stat = {
                'feature_name': feature_name,
                'short_name': short_name,
                'boruta_confirmed': is_confirmed,
                'boruta_tentative': is_tentative,
                'boruta_ranking': ranking,
                'importance_score': importance_score,
                'z_score': z_score,
                **basic_stats
            }
            
            feature_stats.append(feature_stat)
        
        return feature_stats
    
    def extract_rf_feature_statistics(self, rf_model, feature_cols, importances, train_data, group_col):
        """提取随机森林特征统计信息（Boruta替代方案）"""
        logger.info("提取随机森林特征详细统计信息...")
        
        feature_stats = []
        
        # 获取特征数据和分组信息
        y_data = train_data[group_col]
        groups = y_data.unique()
        
        for i, feature_name in enumerate(feature_cols):
            if feature_name not in train_data.columns:
                continue
                
            feature_data = train_data[feature_name]
            
            # 随机森林重要性
            importance_score = importances[i] if i < len(importances) else 0
            ranking = len(importances) - i  # 基于重要性的排名
            
            # 基本分布统计
            basic_stats = self.calculate_feature_distribution_stats(feature_data, y_data, groups)
            
            # 分类学信息
            short_name = self.get_short_feature_name(feature_name)
            
            feature_stat = {
                'feature_name': feature_name,
                'short_name': short_name,
                'boruta_confirmed': importance_score > np.percentile(importances, 70),
                'boruta_tentative': np.percentile(importances, 50) <= importance_score <= np.percentile(importances, 70),
                'boruta_ranking': ranking,
                'importance_score': importance_score,
                'z_score': 0,  # RF不计算Z分数
                **basic_stats
            }
            
            feature_stats.append(feature_stat)
        
        return feature_stats
    
    def calculate_feature_distribution_stats(self, feature_data, group_data, groups):
        """计算特征的分布统计指标"""
        try:
            from scipy import stats
        except ImportError:
            logger.warning("scipy未安装，跳过高级统计检验")
            # 返回基本统计信息
            return {
                'mean': feature_data.mean(),
                'median': feature_data.median(),
                'std': feature_data.std(),
                'prevalence': (feature_data > 0).sum() / len(feature_data)
            }
        
        stats_dict = {}
        
        # 基本分布统计
        stats_dict.update({
            'mean': feature_data.mean(),
            'median': feature_data.median(),
            'std': feature_data.std(),
            'var': feature_data.var(),
            'min': feature_data.min(),
            'max': feature_data.max(),
            'q25': feature_data.quantile(0.25),
            'q75': feature_data.quantile(0.75),
            'skewness': stats.skew(feature_data),
            'kurtosis': stats.kurtosis(feature_data)
        })
        
        # 质量指标
        non_zero_count = (feature_data > 0).sum()
        total_count = len(feature_data)
        stats_dict.update({
            'prevalence': non_zero_count / total_count,
            'non_zero_count': non_zero_count,
            'zero_count': total_count - non_zero_count,
            'sparsity': 1 - (non_zero_count / total_count)
        })
        
        # 分组间统计
        if len(groups) == 2:
            group1_data = feature_data[group_data == groups[0]]
            group2_data = feature_data[group_data == groups[1]]
            
            # t检验
            try:
                t_stat, p_value = stats.ttest_ind(group1_data, group2_data)
                stats_dict.update({
                    'group1_name': groups[0],
                    'group2_name': groups[1],
                    'group1_mean': group1_data.mean(),
                    'group2_mean': group2_data.mean(),
                    'group1_std': group1_data.std(),
                    'group2_std': group2_data.std(),
                    'fold_change': group2_data.mean() / (group1_data.mean() + 1e-8),
                    'log2_fold_change': np.log2((group2_data.mean() + 1e-8) / (group1_data.mean() + 1e-8)),
                    't_statistic': t_stat,
                    'p_value': p_value,
                    'significant': p_value < 0.05
                })
                
                # Mann-Whitney U检验（非参数）
                u_stat, u_p_value = stats.mannwhitneyu(group1_data, group2_data, alternative='two-sided')
                stats_dict.update({
                    'mannwhitney_u_stat': u_stat,
                    'mannwhitney_p_value': u_p_value,
                    'mannwhitney_significant': u_p_value < 0.05
                })
                
            except Exception as e:
                logger.warning(f"统计检验失败: {e}")
                stats_dict.update({
                    'group1_name': groups[0],
                    'group2_name': groups[1],
                    'group1_mean': group1_data.mean(),
                    'group2_mean': group2_data.mean(),
                    'group1_std': group1_data.std(),
                    'group2_std': group2_data.std(),
                    'fold_change': np.nan,
                    'log2_fold_change': np.nan,
                    't_statistic': np.nan,
                    'p_value': np.nan,
                    'significant': False,
                    'mannwhitney_u_stat': np.nan,
                    'mannwhitney_p_value': np.nan,
                    'mannwhitney_significant': False
                })
        
        return stats_dict

    def apply_multiple_comparison_correction(self, feature_stats_list):
        """对所有特征的p值进行多重比较校正 (Benjamini-Hochberg FDR)"""
        if not feature_stats_list:
            return feature_stats_list
            
        logger.info("应用多重比较校正 (Benjamini-Hochberg FDR)...")
        
        # 提取所有p值
        p_values = []
        valid_indices = []
        
        for i, stat in enumerate(feature_stats_list):
            if 'p_value' in stat and not pd.isna(stat['p_value']):
                p_values.append(stat['p_value'])
                valid_indices.append(i)
        
        if len(p_values) == 0:
            logger.warning("没有有效的p值进行校正")
            return feature_stats_list
        
        # 应用Benjamini-Hochberg FDR校正
        try:
            from scipy.stats import false_discovery_control
            # 使用scipy的新函数 (scipy >= 1.7.0)
            adj_p_values = false_discovery_control(p_values, method='bh')
        except (ImportError, AttributeError):
            # 回退到手动实现Benjamini-Hochberg
            adj_p_values = self.benjamini_hochberg_correction(p_values)
        
        # 同样对Mann-Whitney U检验p值进行校正
        mw_p_values = []
        mw_valid_indices = []
        
        for i, stat in enumerate(feature_stats_list):
            if 'mannwhitney_p_value' in stat and not pd.isna(stat['mannwhitney_p_value']):
                mw_p_values.append(stat['mannwhitney_p_value'])
                mw_valid_indices.append(i)
        
        if len(mw_p_values) > 0:
            try:
                from scipy.stats import false_discovery_control
                adj_mw_p_values = false_discovery_control(mw_p_values, method='bh')
            except (ImportError, AttributeError):
                adj_mw_p_values = self.benjamini_hochberg_correction(mw_p_values)
        else:
            adj_mw_p_values = []
        
        # 更新特征统计中的p值
        corrected_stats = []
        for i, stat in enumerate(feature_stats_list):
            stat_copy = stat.copy()
            
            # 更新t检验p值
            if i in valid_indices:
                adj_idx = valid_indices.index(i)
                stat_copy['p_value_raw'] = stat_copy['p_value']  # 保存原始p值
                stat_copy['p_value'] = adj_p_values[adj_idx]     # 使用校正后的p值
                stat_copy['p_value_adj'] = adj_p_values[adj_idx] # 明确标记为校正后p值
                stat_copy['significant'] = adj_p_values[adj_idx] < 0.05
            
            # 更新Mann-Whitney U检验p值
            if i in mw_valid_indices:
                mw_adj_idx = mw_valid_indices.index(i)
                stat_copy['mannwhitney_p_value_raw'] = stat_copy['mannwhitney_p_value']
                stat_copy['mannwhitney_p_value'] = adj_mw_p_values[mw_adj_idx]
                stat_copy['mannwhitney_p_value_adj'] = adj_mw_p_values[mw_adj_idx]
                stat_copy['mannwhitney_significant'] = adj_mw_p_values[mw_adj_idx] < 0.05
            
            corrected_stats.append(stat_copy)
        
        # 统计校正效果
        original_significant = sum(1 for stat in feature_stats_list 
                                 if stat.get('significant', False))
        corrected_significant = sum(1 for stat in corrected_stats 
                                  if stat.get('significant', False))
        
        logger.info(f"多重比较校正结果:")
        logger.info(f"  总特征数: {len(feature_stats_list)}")
        logger.info(f"  有效p值数: {len(p_values)}")
        logger.info(f"  校正前显著: {original_significant}")
        logger.info(f"  校正后显著: {corrected_significant}")
        
        return corrected_stats
    
    def benjamini_hochberg_correction(self, p_values):
        """手动实现Benjamini-Hochberg FDR校正"""
        import numpy as np
        
        p_values = np.array(p_values)
        sorted_indices = np.argsort(p_values)
        sorted_p = p_values[sorted_indices]
        m = len(p_values)
        
        # 计算校正后的p值
        adj_p = np.zeros_like(sorted_p)
        adj_p[-1] = sorted_p[-1]  # 最大的p值不变
        
        for i in range(m - 2, -1, -1):
            adj_p[i] = min(adj_p[i + 1], (m / (i + 1)) * sorted_p[i])
        
        # 确保校正后的p值不超过1
        adj_p = np.minimum(adj_p, 1.0)
        
        # 恢复原始顺序
        result = np.zeros_like(adj_p)
        result[sorted_indices] = adj_p
        
        return result

    def convert_numpy_types(self, obj):
        """递归转换numpy类型为Python原生类型，解决JSON序列化问题"""
        if isinstance(obj, dict):
            return {key: self.convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self.convert_numpy_types(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj):
            return None
        else:
            return obj


def main():
    """主函数"""
    try:
        # 创建流程实例
        pipeline = BorutaPipelinePython()

        # 运行流程
        results = pipeline.run_pipeline()

        print("\n" + "="*80)
        print("Boruta特征选择流程完成！(快速版本)")
        print("="*80)
        print("处理结果:")
        total_features = 0
        for result in results:
            print(f"  {result['level']}: {len(result['features'])} 个特征")
            print(f"    训练集: {result['train_shape']}")
            print(f"    验证集: {result['val_shape']}")
            total_features += len(result['features'])
        print("-" * 40)
        print(f"总特征数: {total_features}")
        print("="*80)
        print("✅ 已生成文件:")
        print("  - 各分类水平独立数据集（仅显著差异特征，使用简称）")
        print("  - 整合的训练集和验证集（无前缀，直接使用简称）")
        print("  - 详细的特征统计报告")
        print("  - 特征列表和汇总文件（使用简称）")
        print("  - 核心统计指标（仅显著特征，FDR校正p值）")
        print("="*80)

    except Exception as e:
        logger.error(f"流程执行失败: {e}")
        raise


if __name__ == "__main__":
    main()
