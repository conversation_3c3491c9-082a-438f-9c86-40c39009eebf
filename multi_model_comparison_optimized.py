import os
import glob
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import joblib
import json
import warnings
from scipy.stats import gmean
from scipy import stats
from collections import defaultdict

from sklearn.model_selection import (
    RepeatedStratifiedKFold, StratifiedKFold, GridSearchCV, 
    cross_val_score, permutation_test_score
)
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_curve, auc, confusion_matrix, balanced_accuracy_score,
    classification_report, roc_auc_score
)
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression, RidgeClassifier
from sklearn.svm import SVC
from sklearn.decomposition import PCA
from sklearn.feature_selection import RFECV
from sklearn.pipeline import Pipeline
from sklearn.utils import resample

# 抑制警告
warnings.filterwarnings('ignore')

# --- 全局配置 ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

class SmallSampleMicrobiomeEvaluator:
    """
    专门针对小样本微生物数据的机器学习评估器
    基于最新文献的最佳实践：
    1. 严格的CLR转换和零值处理
    2. 小样本优化的模型选择
    3. 多重验证策略防止过拟合
    4. 稳健性和泛化能力评估
    """
    
    def __init__(self):
        """初始化评估器"""
        # 基于文献推荐的小样本模型（按优先级排序）
        self.models = {
            "Ridge": RidgeClassifier(random_state=42),
            "RandomForest": RandomForestClassifier(random_state=42, class_weight='balanced'),
            "LogisticRegression": LogisticRegression(random_state=42, max_iter=1000, 
                                                   solver='liblinear', class_weight='balanced'),
            "SVM_RBF": SVC(random_state=42, probability=True, class_weight='balanced')
        }
        
        # 小样本优化的超参数网格
        self.param_grids = {
            "Ridge": {
                'alpha': [0.01, 0.1, 1.0, 10.0, 100.0]  # 强正则化
            },
            "RandomForest": {
                'n_estimators': [50, 100],
                'max_depth': [2, 3, 4],  # 限制深度防止过拟合
                'min_samples_leaf': [5, 10, 15],  # 增加最小叶子节点
                'max_features': ['sqrt', 'log2']  # 特征子采样
            },
            "LogisticRegression": {
                'C': [0.001, 0.01, 0.1, 1.0],  # 强正则化
                'penalty': ['l2']
            },
            "SVM_RBF": {
                'C': [0.1, 1.0, 10.0],
                'gamma': ['scale', 'auto', 0.001, 0.01]
            }
        }
        
        self.results = {}
        self.stability_results = {}
        self.output_dir = self._create_output_directory()
        self.logger = self._setup_file_logger()
        self._setup_publication_style()
        
    def _create_output_directory(self):
        """创建输出目录"""
        desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        dir_path = os.path.join(desktop_path, f"SmallSample_Microbiome_Analysis_{timestamp}")
        os.makedirs(dir_path, exist_ok=True)
        for subdir in ['plots', 'models', 'stability_analysis']:
            os.makedirs(os.path.join(dir_path, subdir), exist_ok=True)
        return dir_path
        
    def _setup_file_logger(self):
        """设置日志记录"""
        logger = logging.getLogger(__name__)
        log_file = os.path.join(self.output_dir, 'analysis_log.log')
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        return logger
        
    def _setup_publication_style(self):
        """设置顶刊可视化风格"""
        plt.style.use('default')
        plt.rcParams.update({
            'font.family': 'Times New Roman',
            'font.size': 8,
            'axes.labelweight': 'bold',
            'axes.titleweight': 'bold',
            'figure.figsize': (3.5, 2.5),
            'figure.dpi': 600,  # 提高DPI
            'savefig.dpi': 600,
            'savefig.bbox': 'tight',
            'savefig.pad_inches': 0.05,
            'axes.grid': False,
            'axes.facecolor': 'white',
            'figure.facecolor': 'white',
            'axes.linewidth': 0.5,
            'xtick.major.width': 0.5,
            'ytick.major.width': 0.5
        })
        self.logger.info("已设置顶刊级可视化风格")
        
    def load_data(self):
        """加载数据"""
        self.logger.info("=== 1. 数据加载 ===")
        desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
        search_pattern = os.path.join(desktop_path, 'LGBM_RFE_Final_Features_*')
        
        list_of_dirs = glob.glob(search_pattern)
        if not list_of_dirs:
            raise FileNotFoundError(f"未找到LGBM RFE输出目录")
        
        latest_dir = max(list_of_dirs, key=os.path.getctime)
        self.logger.info(f"使用目录: {latest_dir}")

        train_files = glob.glob(os.path.join(latest_dir, 'FINAL_FEATURES_TRAIN_*.csv'))
        val_files = glob.glob(os.path.join(latest_dir, 'FINAL_FEATURES_VALIDATION_*.csv'))

        if not train_files or not val_files:
            raise FileNotFoundError("未找到训练或验证文件")

        train_df = pd.read_csv(train_files[0])
        val_df = pd.read_csv(val_files[0])
        
        self.logger.info(f"数据加载完成:")
        self.logger.info(f"  训练集: {train_df.shape}")
        self.logger.info(f"  验证集: {val_df.shape}")
        
        return train_df, val_df
        
    def advanced_clr_transform(self, X_train, X_val):
        """
        基于最新文献的严格CLR转换
        使用乘性替换处理零值
        """
        self.logger.info("=== 2. 高级CLR转换 ===")
        
        # 1. 数据质量检查
        zero_prop_train = (X_train == 0).sum().sum() / X_train.size
        zero_prop_val = (X_val == 0).sum().sum() / X_val.size
        self.logger.info(f"训练集零值比例: {zero_prop_train:.2%}")
        self.logger.info(f"验证集零值比例: {zero_prop_val:.2%}")
        
        # 2. 乘性替换零值处理
        # 计算每个特征的最小正值
        min_positive_per_feature = X_train[X_train > 0].min()
        replacement_values = min_positive_per_feature * 0.5
        
        # 替换零值
        X_train_replaced = X_train.copy()
        X_val_replaced = X_val.copy()
        
        for col in X_train.columns:
            X_train_replaced.loc[X_train[col] == 0, col] = replacement_values[col]
            X_val_replaced.loc[X_val[col] == 0, col] = replacement_values[col]
        
        self.logger.info("零值替换完成")
        
        # 3. CLR转换
        def clr_transform(data):
            # 计算几何平均数
            geom_means = data.apply(lambda x: gmean(x), axis=1)
            # CLR转换
            clr_data = data.div(geom_means, axis=0).apply(np.log)
            return clr_data
        
        X_train_clr = clr_transform(X_train_replaced)
        X_val_clr = clr_transform(X_val_replaced)
        
        # 4. 验证CLR性质（每行和为0）
        train_sums = X_train_clr.sum(axis=1)
        val_sums = X_val_clr.sum(axis=1)
        
        if not np.allclose(train_sums, 0, atol=1e-10):
            self.logger.warning("训练集CLR转换验证失败")
        if not np.allclose(val_sums, 0, atol=1e-10):
            self.logger.warning("验证集CLR转换验证失败")
        else:
            self.logger.info("CLR转换验证通过")
            
        return X_train_clr, X_val_clr
        
    def preprocess_data(self, train_df, val_df):
        """数据预处理"""
        # 分离特征和标签
        feature_cols = [col for col in train_df.columns if col not in ['SampleID', 'Group']]
        X_train = train_df[feature_cols]
        y_train = train_df['Group']
        X_val = val_df[feature_cols]
        y_val = val_df['Group']
        
        # CLR转换
        X_train_clr, X_val_clr = self.advanced_clr_transform(X_train, X_val)
        
        # 标签编码
        le = LabelEncoder()
        y_train_encoded = le.fit_transform(y_train)
        y_val_encoded = le.transform(y_val)
        self.label_encoder = le
        
        self.logger.info(f"预处理完成，类别: {le.classes_}")
        
        return X_train_clr, y_train_encoded, X_val_clr, y_val_encoded, feature_cols

    def robust_model_evaluation(self, X_train, y_train, X_val, y_val, feature_cols):
        """
        基于文献的稳健模型评估
        包含多重验证和稳定性分析
        """
        self.logger.info("=== 3. 稳健模型评估 ===")

        # 小样本交叉验证策略
        cv_outer = RepeatedStratifiedKFold(n_splits=5, n_repeats=10, random_state=42)
        cv_inner = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)

        for name, model in self.models.items():
            self.logger.info(f"--- 评估模型: {name} ---")

            # 1. 嵌套交叉验证寻找最佳参数
            param_grid = self.param_grids[name]
            grid_search = GridSearchCV(
                model, param_grid, cv=cv_inner,
                scoring='f1', n_jobs=-1, verbose=0
            )

            # 2. 外层交叉验证评估泛化性能
            cv_scores = cross_val_score(
                grid_search, X_train, y_train,
                cv=cv_outer, scoring='f1', n_jobs=-1
            )

            # 3. 训练最终模型
            grid_search.fit(X_train, y_train)
            best_model = grid_search.best_estimator_

            # 4. 验证集预测
            y_pred_val = best_model.predict(X_val)
            y_proba_val = best_model.predict_proba(X_val)[:, 1] if hasattr(best_model, 'predict_proba') else None

            # 5. 计算全面的性能指标
            metrics = self._calculate_comprehensive_metrics(y_val, y_pred_val, y_proba_val)

            # 6. 特征重要性
            feature_importance = self._extract_feature_importance(best_model, feature_cols)

            # 7. 稳定性分析
            stability_metrics = self._stability_analysis(best_model, X_train, y_train, cv_outer)

            # 存储结果
            self.results[name] = {
                'model': best_model,
                'best_params': grid_search.best_params_,
                'cv_scores': cv_scores,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'metrics': metrics,
                'predictions': {
                    'y_pred_val': y_pred_val,
                    'y_proba_val': y_proba_val
                },
                'feature_importance': feature_importance,
                'stability': stability_metrics
            }

            self.logger.info(f"{name} - CV F1: {cv_scores.mean():.3f}±{cv_scores.std():.3f}")
            self.logger.info(f"{name} - Val F1: {metrics['F1-Score']:.3f}")

        # 确定最佳模型（基于交叉验证性能）
        self.best_model_name = max(self.results, key=lambda x: self.results[x]['cv_mean'])
        self.logger.info(f"最佳模型: {self.best_model_name}")

    def _calculate_comprehensive_metrics(self, y_true, y_pred, y_proba=None):
        """计算全面的性能指标"""
        metrics = {
            'Accuracy': accuracy_score(y_true, y_pred),
            'Balanced_Accuracy': balanced_accuracy_score(y_true, y_pred),
            'Precision': precision_score(y_true, y_pred, average='weighted'),
            'Recall': recall_score(y_true, y_pred, average='weighted'),
            'F1-Score': f1_score(y_true, y_pred, average='weighted'),
        }

        if y_proba is not None:
            metrics['AUC'] = roc_auc_score(y_true, y_proba)

        return metrics

    def _extract_feature_importance(self, model, feature_cols):
        """提取特征重要性"""
        if hasattr(model, 'feature_importances_'):
            return dict(zip(feature_cols, model.feature_importances_))
        elif hasattr(model, 'coef_'):
            # 处理不同形状的系数
            coef = model.coef_
            if coef.ndim == 1:
                return dict(zip(feature_cols, np.abs(coef)))
            else:
                return dict(zip(feature_cols, np.abs(coef[0])))
        else:
            return None

    def _stability_analysis(self, model, X, y, cv):
        """稳定性分析"""
        feature_selections = []
        performance_variations = []

        for train_idx, test_idx in cv.split(X, y):
            X_train_fold = X.iloc[train_idx]
            y_train_fold = y[train_idx]
            X_test_fold = X.iloc[test_idx]
            y_test_fold = y[test_idx]

            # 训练模型
            model_copy = type(model)(**model.get_params())
            model_copy.fit(X_train_fold, y_train_fold)

            # 性能变异
            y_pred_fold = model_copy.predict(X_test_fold)
            f1_fold = f1_score(y_test_fold, y_pred_fold, average='weighted')
            performance_variations.append(f1_fold)

            # 特征选择稳定性（如果适用）
            if hasattr(model_copy, 'feature_importances_'):
                top_features = np.argsort(model_copy.feature_importances_)[-5:]
                feature_selections.append(set(top_features))

        # 计算稳定性指标
        stability_metrics = {
            'performance_std': np.std(performance_variations),
            'performance_cv': np.std(performance_variations) / np.mean(performance_variations),
        }

        if feature_selections:
            # Jaccard稳定性指数
            jaccard_scores = []
            for i in range(len(feature_selections)):
                for j in range(i+1, len(feature_selections)):
                    intersection = len(feature_selections[i] & feature_selections[j])
                    union = len(feature_selections[i] | feature_selections[j])
                    jaccard_scores.append(intersection / union if union > 0 else 0)

            stability_metrics['feature_stability'] = np.mean(jaccard_scores)

        return stability_metrics

    def generate_enhanced_visualizations(self, X_train, y_train, X_val, y_val, feature_cols):
        """生成增强的可视化图表"""
        self.logger.info("=== 4. 生成可视化图表 ===")

        # 1. 模型性能对比（包含稳定性）
        self._plot_model_performance_with_stability()

        # 2. 交叉验证性能分布
        self._plot_cv_performance_distribution()

        # 3. 特征重要性稳定性分析
        self._plot_feature_importance_stability(feature_cols)

        # 4. ROC曲线对比
        self._plot_enhanced_roc_curves(y_val)

        # 5. 学习曲线（小样本优化）
        self._plot_small_sample_learning_curves(X_train, y_train)

        # 6. 混淆矩阵热图
        self._plot_confusion_matrices(y_val)

        # 7. 数据质量评估
        self._plot_data_quality_assessment(X_train, y_train, X_val, y_val)

        self.logger.info("可视化图表生成完成")

    def _plot_model_performance_with_stability(self):
        """模型性能与稳定性对比"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(7, 3))

        models = list(self.results.keys())
        cv_means = [self.results[m]['cv_mean'] for m in models]
        cv_stds = [self.results[m]['cv_std'] for m in models]
        val_f1s = [self.results[m]['metrics']['F1-Score'] for m in models]

        # 性能对比
        x_pos = np.arange(len(models))
        ax1.bar(x_pos, cv_means, yerr=cv_stds, capsize=3, alpha=0.7, color='skyblue')
        ax1.scatter(x_pos, val_f1s, color='red', s=50, zorder=5, label='Validation')
        ax1.set_xlabel('Models')
        ax1.set_ylabel('F1-Score')
        ax1.set_title('Model Performance Comparison')
        ax1.set_xticks(x_pos)
        ax1.set_xticklabels(models, rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 稳定性分析
        stability_scores = []
        for m in models:
            if 'feature_stability' in self.results[m]['stability']:
                stability_scores.append(self.results[m]['stability']['feature_stability'])
            else:
                stability_scores.append(0)

        performance_cvs = [self.results[m]['stability']['performance_cv'] for m in models]

        ax2.scatter(performance_cvs, stability_scores, s=100, alpha=0.7)
        for i, model in enumerate(models):
            ax2.annotate(model, (performance_cvs[i], stability_scores[i]),
                        xytext=(5, 5), textcoords='offset points', fontsize=7)
        ax2.set_xlabel('Performance CV')
        ax2.set_ylabel('Feature Stability')
        ax2.set_title('Stability Analysis')
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        self._save_plot(fig, 'model_performance_stability')

    def _plot_cv_performance_distribution(self):
        """交叉验证性能分布"""
        fig, ax = plt.subplots(figsize=(4, 3))

        cv_data = []
        labels = []
        for name, results in self.results.items():
            cv_data.append(results['cv_scores'])
            labels.append(name)

        bp = ax.boxplot(cv_data, labels=labels, patch_artist=True)

        # 美化箱线图
        colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightyellow']
        for patch, color in zip(bp['boxes'], colors[:len(bp['boxes'])]):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        ax.set_ylabel('F1-Score')
        ax.set_title('Cross-Validation Performance Distribution')
        ax.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        self._save_plot(fig, 'cv_performance_distribution')

    def _plot_feature_importance_stability(self, feature_cols):
        """特征重要性稳定性分析"""
        importance_models = {name: res for name, res in self.results.items()
                           if res['feature_importance'] is not None}

        if not importance_models:
            return

        fig, ax = plt.subplots(figsize=(6, 4))

        # 创建特征重要性矩阵
        importance_matrix = []
        model_names = []

        for name, results in importance_models.items():
            importance_values = [results['feature_importance'].get(feat, 0) for feat in feature_cols]
            importance_matrix.append(importance_values)
            model_names.append(name)

        importance_df = pd.DataFrame(importance_matrix, columns=feature_cols, index=model_names)

        # 绘制热图
        sns.heatmap(importance_df, annot=True, fmt='.3f', cmap='YlOrRd',
                   ax=ax, cbar_kws={'shrink': 0.8})
        ax.set_title('Feature Importance Across Models')
        ax.set_xlabel('Features')
        ax.set_ylabel('Models')

        plt.tight_layout()
        self._save_plot(fig, 'feature_importance_stability')

    def _plot_enhanced_roc_curves(self, y_val):
        """增强的ROC曲线"""
        fig, ax = plt.subplots(figsize=(4, 3))

        colors = ['blue', 'green', 'red', 'orange']
        for i, (name, results) in enumerate(self.results.items()):
            if results['predictions']['y_proba_val'] is not None:
                fpr, tpr, _ = roc_curve(y_val, results['predictions']['y_proba_val'])
                auc_score = results['metrics'].get('AUC', 0)
                ax.plot(fpr, tpr, color=colors[i % len(colors)],
                       label=f'{name} (AUC={auc_score:.3f})', linewidth=2)

        ax.plot([0, 1], [0, 1], 'k--', alpha=0.5)
        ax.set_xlabel('False Positive Rate')
        ax.set_ylabel('True Positive Rate')
        ax.set_title('ROC Curves Comparison')
        ax.legend(loc='lower right')
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        self._save_plot(fig, 'enhanced_roc_curves')

    def _save_plot(self, fig, name):
        """保存图表"""
        for ext in ['png', 'pdf', 'eps']:
            path = os.path.join(self.output_dir, 'plots', f"{name}.{ext}")
            fig.savefig(path, dpi=600, bbox_inches='tight', pad_inches=0.05)
        plt.close(fig)

    def _plot_small_sample_learning_curves(self, X, y):
        """小样本学习曲线"""
        from sklearn.model_selection import learning_curve

        fig, axes = plt.subplots(2, 2, figsize=(8, 6))
        axes = axes.ravel()

        # 小样本优化的训练集大小
        n_samples = len(y)
        train_sizes = np.linspace(0.3, 1.0, 5)

        for i, (name, model) in enumerate(self.models.items()):
            if i >= 4:  # 最多显示4个模型
                break

            ax = axes[i]

            try:
                cv_learning = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)
                train_sizes_abs, train_scores, val_scores = learning_curve(
                    model, X, y, train_sizes=train_sizes, cv=cv_learning,
                    scoring='f1', n_jobs=-1, random_state=42
                )

                train_mean = np.mean(train_scores, axis=1)
                train_std = np.std(train_scores, axis=1)
                val_mean = np.mean(val_scores, axis=1)
                val_std = np.std(val_scores, axis=1)

                ax.plot(train_sizes_abs, train_mean, 'o-', color='blue', label='Training')
                ax.fill_between(train_sizes_abs, train_mean - train_std,
                               train_mean + train_std, alpha=0.1, color='blue')

                ax.plot(train_sizes_abs, val_mean, 'o-', color='red', label='Validation')
                ax.fill_between(train_sizes_abs, val_mean - val_std,
                               val_mean + val_std, alpha=0.1, color='red')

                ax.set_title(f'{name}')
                ax.set_xlabel('Training Size')
                ax.set_ylabel('F1-Score')
                ax.legend()
                ax.grid(True, alpha=0.3)

            except Exception as e:
                ax.text(0.5, 0.5, f'Error: {str(e)[:30]}...',
                       ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'{name} (Error)')

        plt.tight_layout()
        self._save_plot(fig, 'small_sample_learning_curves')

    def _plot_confusion_matrices(self, y_val):
        """混淆矩阵热图"""
        fig, axes = plt.subplots(2, 2, figsize=(8, 6))
        axes = axes.ravel()

        for i, (name, results) in enumerate(self.results.items()):
            if i >= 4:
                break

            ax = axes[i]
            cm = confusion_matrix(y_val, results['predictions']['y_pred_val'])

            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax,
                       xticklabels=self.label_encoder.classes_,
                       yticklabels=self.label_encoder.classes_)
            ax.set_title(f'{name}')
            ax.set_xlabel('Predicted')
            ax.set_ylabel('Actual')

        plt.tight_layout()
        self._save_plot(fig, 'confusion_matrices')

    def _plot_data_quality_assessment(self, X_train, y_train, X_val, y_val):
        """数据质量评估"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(8, 6))

        # 1. 特征分布
        feature_means = X_train.mean()
        ax1.hist(feature_means, bins=10, alpha=0.7, color='skyblue')
        ax1.set_title('Feature Mean Distribution')
        ax1.set_xlabel('Mean Value')
        ax1.set_ylabel('Frequency')

        # 2. 类别平衡
        unique, counts = np.unique(y_train, return_counts=True)
        class_names = [self.label_encoder.classes_[i] for i in unique]
        ax2.bar(class_names, counts, alpha=0.7, color=['lightcoral', 'lightblue'])
        ax2.set_title('Class Distribution (Training)')
        ax2.set_ylabel('Count')

        # 3. 特征相关性
        corr_matrix = X_train.corr()
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        sns.heatmap(corr_matrix, mask=mask, cmap='coolwarm', center=0, ax=ax3,
                   square=True, cbar_kws={'shrink': 0.5})
        ax3.set_title('Feature Correlations')

        # 4. 训练-验证分布对比
        train_mean = X_train.mean().mean()
        val_mean = X_val.mean().mean()
        ax4.bar(['Training', 'Validation'], [train_mean, val_mean],
               alpha=0.7, color=['green', 'orange'])
        ax4.set_title('Train-Val Distribution')
        ax4.set_ylabel('Mean Feature Value')

        plt.tight_layout()
        self._save_plot(fig, 'data_quality_assessment')

    def save_comprehensive_results(self, feature_cols):
        """保存全面的结果"""
        self.logger.info("=== 5. 保存结果 ===")

        # 1. 性能指标
        metrics_data = []
        for name, results in self.results.items():
            row = {'Model': name}
            row.update(results['metrics'])
            row['CV_Mean'] = results['cv_mean']
            row['CV_Std'] = results['cv_std']
            row.update(results['stability'])
            metrics_data.append(row)

        metrics_df = pd.DataFrame(metrics_data)
        metrics_df.to_csv(os.path.join(self.output_dir, 'comprehensive_metrics.csv'), index=False)

        # 2. 交叉验证详细结果
        cv_results = {}
        for name, results in self.results.items():
            cv_results[name] = results['cv_scores'].tolist()

        with open(os.path.join(self.output_dir, 'cv_detailed_results.json'), 'w') as f:
            json.dump(cv_results, f, indent=4)

        # 3. 特征重要性
        importance_data = {}
        for name, results in self.results.items():
            if results['feature_importance']:
                importance_data[name] = results['feature_importance']

        if importance_data:
            importance_df = pd.DataFrame(importance_data).fillna(0)
            importance_df.to_csv(os.path.join(self.output_dir, 'feature_importance_detailed.csv'))

        # 4. 模型保存
        for name, results in self.results.items():
            model_path = os.path.join(self.output_dir, 'models', f'{name}_optimized.pkl')
            joblib.dump(results['model'], model_path)

        # 5. 分析报告
        self._generate_analysis_report()

        self.logger.info("所有结果已保存")

    def _generate_analysis_report(self):
        """生成分析报告"""
        report_path = os.path.join(self.output_dir, 'analysis_report.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=== 小样本微生物数据机器学习分析报告 ===\n\n")

            # 最佳模型
            best_model = self.results[self.best_model_name]
            f.write(f"最佳模型: {self.best_model_name}\n")
            f.write(f"交叉验证F1-Score: {best_model['cv_mean']:.3f} ± {best_model['cv_std']:.3f}\n")
            f.write(f"验证集F1-Score: {best_model['metrics']['F1-Score']:.3f}\n\n")

            # 模型排名
            f.write("模型性能排名 (基于交叉验证):\n")
            sorted_models = sorted(self.results.items(),
                                 key=lambda x: x[1]['cv_mean'], reverse=True)
            for i, (name, results) in enumerate(sorted_models, 1):
                f.write(f"{i}. {name}: {results['cv_mean']:.3f} ± {results['cv_std']:.3f}\n")

            f.write("\n=== 建议 ===\n")
            f.write("1. 基于交叉验证结果选择最佳模型\n")
            f.write("2. 注意模型稳定性指标\n")
            f.write("3. 考虑特征重要性的一致性\n")
            f.write("4. 建议在更大数据集上验证结果\n")

    def run(self):
        """执行完整分析流程"""
        try:
            self.logger.info("=== 开始小样本微生物数据分析 ===")

            # 1. 加载数据
            train_df, val_df = self.load_data()

            # 2. 预处理
            X_train, y_train, X_val, y_val, feature_cols = self.preprocess_data(train_df, val_df)

            # 3. 模型评估
            self.robust_model_evaluation(X_train, y_train, X_val, y_val, feature_cols)

            # 4. 可视化
            self.generate_enhanced_visualizations(X_train, y_train, X_val, y_val, feature_cols)

            # 5. 保存结果
            self.save_comprehensive_results(feature_cols)

            self.logger.info("=== 分析完成 ===")

        except Exception as e:
            self.logger.error(f"分析过程中发生错误: {e}", exc_info=True)
            raise

if __name__ == "__main__":
    evaluator = SmallSampleMicrobiomeEvaluator()
    evaluator.run()
