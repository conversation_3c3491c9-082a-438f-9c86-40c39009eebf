import os
import glob
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import joblib
import warnings
from scipy.stats import gmean, pearsonr
from mpl_toolkits.mplot3d import Axes3D

from sklearn.model_selection import StratifiedKFold, RepeatedStratifiedKFold
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_curve, auc, confusion_matrix, roc_auc_score
)
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
from sklearn.decomposition import PCA

# 抑制警告
warnings.filterwarnings('ignore')

# --- 全局配置 ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

class LDAShrinkageTrainer:
    """
    LDA_Shrinkage模型完整训练器
    专门针对微生物数据的线性判别分析
    包含完整的判别方程分析和最优阈值寻找
    """
    
    def __init__(self):
        """初始化训练器"""
        # LDA_Shrinkage模型配置
        self.model = LinearDiscriminantAnalysis(solver='lsqr', shrinkage='auto')
        self.model_name = "LDA_Shrinkage"
        
        # 结果存储
        self.results = {}
        self.discriminant_analysis = {}
        self.optimal_threshold = None
        
        # 创建输出目录
        self.output_dir = self._create_output_directory()
        self.logger = self._setup_file_logger()
        
        # 顶级科学期刊专业配色方案 - 优化版
        self.color_palette = {
            'primary': '#2E86AB',      # 深蓝色 - CL组 (色盲友好)
            'secondary': '#A23B72',    # 深紫红 - WL组 (色盲友好)
            'accent': '#F18F01',       # 橙色 - 强调色
            'success': '#C73E1D',      # 深红色 - 成功/重要
            'neutral': '#6C757D',      # 中性灰
            'light': '#E9ECEF',        # 浅灰背景
            'dark': '#212529',         # 深色文字
            'warning': '#FFC107',      # 警告黄
            'info': '#17A2B8',         # 信息蓝
            'positive': '#28A745',     # 正向绿
            'negative': '#DC3545'      # 负向红
        }

        # 色盲友好的科学期刊配色 (基于ColorBrewer)
        self.colorblind_safe = {
            'qualitative': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f'],
            'diverging': ['#d73027', '#f46d43', '#fdae61', '#fee08b', '#e6f598', '#abdda4', '#66c2a5', '#3288bd'],
            'sequential_blue': ['#f7fbff', '#deebf7', '#c6dbef', '#9ecae1', '#6baed6', '#4292c6', '#2171b5', '#084594'],
            'sequential_red': ['#fff5f0', '#fee0d2', '#fcbba1', '#fc9272', '#fb6a4a', '#ef3b2c', '#cb181d', '#99000d']
        }

        # 学术期刊专用高级配色方案
        self.advanced_palettes = {
            'nature_discrete': ['#E64B35', '#4DBBD5', '#00A087', '#3C5488', '#F39B7F', '#8491B4', '#91D1C2', '#DC0000'],
            'science_continuous': ['#0d0887', '#46039f', '#7201a8', '#9c179e', '#bd3786', '#d8576b', '#ed7953', '#fb9f3a', '#fdca26', '#f0f921'],
            'cell_palette': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
            'pnas_colors': ['#1B9E77', '#D95F02', '#7570B3', '#E7298A', '#66A61E', '#E6AB02', '#A6761D', '#666666']
        }

        # 渐变配色方案 - 用于热图和连续数据
        self.gradient_schemes = {
            'blue_white_red': ['#053061', '#2166ac', '#4393c3', '#92c5de', '#d1e5f0', '#f7f7f7', '#fddbc7', '#f4a582', '#d6604d', '#b2182b', '#67001f'],
            'viridis_enhanced': ['#440154', '#482777', '#3f4a8a', '#31678e', '#26838f', '#1f9d8a', '#6cce5a', '#b6de2b', '#fee825'],
            'plasma_scientific': ['#0d0887', '#5302a3', '#8b0aa5', '#b83289', '#db5c68', '#f48849', '#febd2a', '#f0f921'],
            'cividis_colorblind': ['#00224e', '#123570', '#3b496c', '#575d6d', '#707173', '#8a8678', '#a59c74', '#c3b369', '#e1cc55', '#fee838']
        }
        
        # 设置顶刊可视化风格
        self._setup_publication_style()
        
    def _create_output_directory(self):
        """在桌面创建带时间戳的输出目录"""
        desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        dir_path = os.path.join(desktop_path, f"LDA_Shrinkage_Complete_{timestamp}")
        
        # 创建主目录和子目录
        os.makedirs(dir_path, exist_ok=True)
        for subdir in ['plots', 'models', 'results', 'analysis', 'visualization_data']:
            os.makedirs(os.path.join(dir_path, subdir), exist_ok=True)
            
        return dir_path
        
    def _setup_file_logger(self):
        """设置文件日志记录"""
        logger = logging.getLogger(__name__)
        log_file = os.path.join(self.output_dir, 'training_log.log')
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        return logger
        
    def _setup_publication_style(self):
        """设置顶级期刊可视化风格 - 优化版"""
        plt.style.use('default')
        plt.rcParams.update({
            'font.family': ['Times New Roman', 'DejaVu Serif', 'serif'],
            'font.size': 10,  # 增大基础字体
            'axes.labelsize': 11,
            'axes.titlesize': 12,
            'xtick.labelsize': 9,
            'ytick.labelsize': 9,
            'legend.fontsize': 9,
            'axes.labelweight': 'bold',
            'axes.titleweight': 'bold',
            'figure.figsize': (4, 3),  # 适中尺寸便于阅读
            'figure.dpi': 300,
            'savefig.dpi': 300,
            'savefig.bbox': 'tight',
            'savefig.pad_inches': 0.1,
            'axes.grid': False,
            'axes.facecolor': 'white',
            'figure.facecolor': 'white',
            'axes.linewidth': 1.0,  # 增强线条
            'xtick.major.width': 1.0,
            'ytick.major.width': 1.0,
            'legend.frameon': True,  # 添加图例边框提高可读性
            'legend.fancybox': True,
            'legend.shadow': False,
            'legend.framealpha': 0.9,
            'axes.spines.top': False,
            'axes.spines.right': False,
            'axes.spines.left': True,
            'axes.spines.bottom': True,
            'axes.edgecolor': 'black',
            'axes.axisbelow': True
        })
        self.logger.info("已设置优化的顶级期刊可视化风格 (增强可读性)")
        
    def load_data(self):
        """自动读取LGBM RFE输出文件并合并为完整数据集用于交叉验证"""
        self.logger.info("=== 1. 数据加载 ===")
        
        # 在桌面搜索LGBM RFE输出目录
        desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
        search_pattern = os.path.join(desktop_path, 'LGBM_RFE_Final_Features_*')
        
        list_of_dirs = glob.glob(search_pattern)
        if not list_of_dirs:
            raise FileNotFoundError("未找到LGBM RFE输出目录，请确保lgbm_rfe_optimized.py已运行")
        
        # 选择最新的目录
        latest_dir = max(list_of_dirs, key=os.path.getctime)
        self.logger.info(f"使用LGBM RFE输出目录: {latest_dir}")

        # 查找训练和验证文件
        train_files = glob.glob(os.path.join(latest_dir, 'FINAL_FEATURES_TRAIN_*.csv'))
        val_files = glob.glob(os.path.join(latest_dir, 'FINAL_FEATURES_VALIDATION_*.csv'))

        if not train_files or not val_files:
            raise FileNotFoundError("未找到FINAL_FEATURES_TRAIN_*.csv或FINAL_FEATURES_VALIDATION_*.csv文件")

        # 加载并合并数据用于交叉验证
        train_df = pd.read_csv(train_files[0])
        val_df = pd.read_csv(val_files[0])
        
        # 合并训练集和验证集
        full_df = pd.concat([train_df, val_df], ignore_index=True)
        
        self.logger.info(f"数据加载完成:")
        self.logger.info(f"  原训练集: {train_df.shape}")
        self.logger.info(f"  原验证集: {val_df.shape}")
        self.logger.info(f"  合并后完整数据集: {full_df.shape}")
        self.logger.info("将使用5折10次交叉验证进行模型评估")
        
        return full_df
        
    def strict_clr_transform(self, X_train, X_val):
        """
        严谨的CLR转换，与现有脚本保持一致
        避免数据泄露：只使用训练集信息进行转换参数估计
        """
        self.logger.info("=== 2. 严谨CLR转换 ===")
        
        # 1. 数据质量检查
        zero_prop_train = (X_train == 0).sum().sum() / X_train.size
        zero_prop_val = (X_val == 0).sum().sum() / X_val.size
        self.logger.info(f"训练集零值比例: {zero_prop_train:.2%}")
        self.logger.info(f"验证集零值比例: {zero_prop_val:.2%}")
        
        # 2. 基于训练集的乘性替换参数（避免数据泄露）
        min_positive_per_feature = X_train[X_train > 0].min()
        replacement_values = min_positive_per_feature * 0.5
        
        self.logger.info("使用训练集参数进行零值替换，避免数据泄露")
        
        # 3. 应用替换到训练集和验证集
        X_train_replaced = X_train.copy()
        X_val_replaced = X_val.copy()
        
        for col in X_train.columns:
            # 训练集替换
            X_train_replaced.loc[X_train[col] == 0, col] = replacement_values[col]
            # 验证集使用相同参数替换
            X_val_replaced.loc[X_val[col] == 0, col] = replacement_values[col]
        
        # 4. CLR转换函数
        def clr_transform(data):
            """严格的CLR转换"""
            geom_means = data.apply(lambda x: gmean(x), axis=1)
            clr_data = data.div(geom_means, axis=0).apply(np.log)
            return clr_data
        
        # 5. 执行CLR转换
        X_train_clr = clr_transform(X_train_replaced)
        X_val_clr = clr_transform(X_val_replaced)
        
        # 6. 验证CLR性质（每行和应为0）
        train_sums = X_train_clr.sum(axis=1)
        val_sums = X_val_clr.sum(axis=1)
        
        if not np.allclose(train_sums, 0, atol=1e-10):
            self.logger.warning("训练集CLR转换验证失败")
        if not np.allclose(val_sums, 0, atol=1e-10):
            self.logger.warning("验证集CLR转换验证失败")
        else:
            self.logger.info("CLR转换验证通过，满足组成数据约束")
            
        return X_train_clr, X_val_clr
        
    def preprocess_data(self, full_df):
        """数据预处理管道 - 为交叉验证准备数据"""
        # 分离特征和标签
        feature_cols = [col for col in full_df.columns if col not in ['SampleID', 'Group']]
        X_full = full_df[feature_cols]
        y_full = full_df['Group']
        
        # 标签编码
        le = LabelEncoder()
        y_encoded = le.fit_transform(y_full)
        self.label_encoder = le
        
        self.logger.info(f"预处理完成，类别: {le.classes_}")
        self.logger.info(f"特征数量: {len(feature_cols)}")
        self.logger.info(f"样本总数: {len(X_full)}")
        self.logger.info(f"类别分布: {dict(zip(*np.unique(y_encoded, return_counts=True)))}")
        
        return X_full, y_encoded, feature_cols

    def train_lda_model_cv(self, X_full, y_full, feature_cols):
        """使用5折10次交叉验证训练LDA_Shrinkage模型"""
        self.logger.info("=== 3. LDA_Shrinkage模型训练 (5折10次交叉验证) ===")

        # 1. 设置重复分层K折交叉验证
        cv = RepeatedStratifiedKFold(n_splits=5, n_repeats=10, random_state=42)
        self.logger.info("开始5折10次交叉验证 (总共50次训练/测试)...")

        # 2. 存储交叉验证结果
        cv_metrics = {
            'Accuracy': [], 'Precision': [], 'Recall': [], 'F1-Score': [], 'AUC': []
        }
        
        fold_count = 0
        # 保存最后一个fold的数据用于可视化（包含原始和转换后的数据）
        X_last_train_orig, X_last_val_orig = None, None
        X_last_train_clr, X_last_val_clr = None, None
        y_last_train, y_last_val = None, None
        
        # 3. 执行交叉验证
        for train_idx, val_idx in cv.split(X_full, y_full):
            fold_count += 1
            
            # 3.1 划分fold数据
            X_train_fold = X_full.iloc[train_idx]
            X_val_fold = X_full.iloc[val_idx]
            y_train_fold = y_full[train_idx]
            y_val_fold = y_full[val_idx]
            
            # 3.2 在每个fold内独立进行CLR转换（避免数据泄露）
            X_train_clr, X_val_clr = self.strict_clr_transform(X_train_fold, X_val_fold)
            
            # 3.3 训练LDA模型
            model_fold = LinearDiscriminantAnalysis(solver='lsqr', shrinkage='auto')
            model_fold.fit(X_train_clr, y_train_fold)
            
            # 3.4 预测
            y_pred_val = model_fold.predict(X_val_clr)
            y_proba_val = model_fold.predict_proba(X_val_clr)
            
            # 3.5 计算指标
            fold_metrics = self._calculate_core_metrics(y_val_fold, y_pred_val, y_proba_val[:, 1])
            
            # 3.6 记录结果
            for metric_name, value in fold_metrics.items():
                cv_metrics[metric_name].append(value)
            
            # 保存最后一个fold的数据用于可视化
            if fold_count == 50:  # 最后一个fold
                X_last_train_orig, X_last_val_orig = X_train_fold, X_val_fold
                X_last_train_clr, X_last_val_clr = X_train_clr, X_val_clr
                y_last_train, y_last_val = y_train_fold, y_val_fold
                self.model = model_fold  # 保存最后训练的模型
            
            if fold_count % 10 == 0:
                self.logger.info(f"已完成 {fold_count}/50 fold...")

        # 4. 计算交叉验证统计量
        cv_stats = {}
        for metric_name, values in cv_metrics.items():
            cv_stats[metric_name] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'lower_ci': np.percentile(values, 2.5),
                'upper_ci': np.percentile(values, 97.5)
            }

        # 5. 在完整数据集上训练最终模型用于分析
        X_full_clr = self._perform_full_clr_transform(X_full)
        final_model = LinearDiscriminantAnalysis(solver='lsqr', shrinkage='auto')
        final_model.fit(X_full_clr, y_full)
        self.model = final_model

        # 6. 使用最后一个fold的数据进行后续分析和可视化
        y_pred_train = self.model.predict(X_last_train_clr)
        y_proba_train = self.model.predict_proba(X_last_train_clr)
        y_decision_train = self.model.decision_function(X_last_train_clr)

        y_pred_val = self.model.predict(X_last_val_clr)
        y_proba_val = self.model.predict_proba(X_last_val_clr)
        y_decision_val = self.model.decision_function(X_last_val_clr)

        # 7. 寻找最优阈值（基于最后一个fold）
        optimal_threshold = self._find_optimal_threshold(y_last_val, y_decision_val)

        # 8. 计算最后一个fold的训练集指标
        train_metrics = self._calculate_core_metrics(y_last_train, y_pred_train, y_proba_train[:, 1])

        # 9. 详细的判别分析（基于完整数据集模型）
        discriminant_analysis = self._perform_discriminant_analysis(X_full_clr, y_full, feature_cols)

        # 10. 特征重要性分析
        feature_importance = self._extract_feature_importance(feature_cols)

        # 11. 存储所有结果
        self.results = {
            'model': self.model,
            'train_metrics': train_metrics,
            'val_metrics': cv_stats,  # 使用交叉验证统计量
            'predictions': {
                'y_pred_train': y_pred_train,
                'y_proba_train': y_proba_train,
                'y_decision_train': y_decision_train,
                'y_pred_val': y_pred_val,
                'y_proba_val': y_proba_val,
                'y_decision_val': y_decision_val
            },
            'discriminant_analysis': discriminant_analysis,
            'optimal_threshold': optimal_threshold,
            'feature_importance': feature_importance,
            'cv_results': cv_metrics,  # 保存原始交叉验证结果
            'last_fold_data': {  # 保存最后fold的数据用于可视化
                'X_train_orig': X_last_train_orig,
                'X_val_orig': X_last_val_orig,
                'X_train_clr': X_last_train_clr,
                'X_val_clr': X_last_val_clr,
                'y_train': y_last_train,
                'y_val': y_last_val
            }
        }

        # 12. 输出结果摘要
        self._log_cv_training_summary(train_metrics, cv_stats, optimal_threshold)

        return self.results

    def _perform_full_clr_transform(self, X):
        """对完整数据集进行CLR转换用于最终模型训练"""
        # 处理零值
        min_positive_per_feature = X[X > 0].min()
        replacement_values = min_positive_per_feature * 0.5
        
        X_replaced = X.copy()
        for col in X.columns:
            X_replaced.loc[X[col] == 0, col] = replacement_values[col]
        
        # CLR转换
        geom_means = X_replaced.apply(lambda x: gmean(x), axis=1)
        X_clr = X_replaced.div(geom_means, axis=0).apply(np.log)
        
        return X_clr

    def _log_cv_training_summary(self, train_metrics, cv_metrics, optimal_threshold):
        """输出交叉验证训练摘要"""
        self.logger.info("=== 5折10次交叉验证结果摘要 ===")
        self.logger.info("最后fold训练集性能:")
        for metric, value in train_metrics.items():
            self.logger.info(f"  {metric}: {value:.4f}")

        self.logger.info("交叉验证性能 (均值±标准差 [95% CI]):")
        for metric, stats in cv_metrics.items():
            self.logger.info(f"  {metric}: {stats['mean']:.4f}±{stats['std']:.4f} [{stats['lower_ci']:.4f}, {stats['upper_ci']:.4f}]")

        self.logger.info(f"最优判别阈值: {optimal_threshold['optimal_threshold']:.4f}")
        self.logger.info(f"最优阈值下约登指数: {optimal_threshold['optimal_youden_j']:.4f}")

    def _calculate_core_metrics(self, y_true, y_pred, y_proba):
        """计算核心评价指标的单点估计值"""
        metrics = {
            'Accuracy': accuracy_score(y_true, y_pred),
            'Precision': precision_score(y_true, y_pred, average='weighted', zero_division=0),
            'Recall': recall_score(y_true, y_pred, average='weighted', zero_division=0),
            'F1-Score': f1_score(y_true, y_pred, average='weighted', zero_division=0)
        }
        if len(np.unique(y_true)) == 2 and y_proba is not None:
            try:
                metrics['AUC'] = roc_auc_score(y_true, y_proba)
            except ValueError:
                metrics['AUC'] = 0.0
        return metrics

    def _calculate_bootstrapped_metrics(self, y_true, y_decision, y_proba, n_bootstraps=1000):
        """通过自助法计算性能指标的95%置信区间"""
        self.logger.info(f"开始通过自助法计算验证集性能指标 (n={n_bootstraps})...")
        bootstrapped_scores = {
            'Accuracy': [], 'Precision': [], 'Recall': [], 'F1-Score': [], 'AUC': []
        }
        
        # 使用找到的最优阈值进行二分类
        y_pred_optimal = (y_decision > self.optimal_threshold).astype(int)
        
        rng = np.random.RandomState(42)
        n_samples = len(y_true)

        for _ in range(n_bootstraps):
            indices = rng.choice(n_samples, n_samples, replace=True)
            
            # 如果抽样后的样本只包含一个类别，则跳过此次迭代
            if len(np.unique(y_true[indices])) < 2:
                continue

            # 计算各项指标
            bs_accuracy = accuracy_score(y_true[indices], y_pred_optimal[indices])
            bs_precision = precision_score(y_true[indices], y_pred_optimal[indices], average='weighted', zero_division=0)
            bs_recall = recall_score(y_true[indices], y_pred_optimal[indices], average='weighted', zero_division=0)
            bs_f1 = f1_score(y_true[indices], y_pred_optimal[indices], average='weighted', zero_division=0)
            bs_auc = roc_auc_score(y_true[indices], y_proba[indices]) if y_proba is not None else 0.0
            
            bootstrapped_scores['Accuracy'].append(bs_accuracy)
            bootstrapped_scores['Precision'].append(bs_precision)
            bootstrapped_scores['Recall'].append(bs_recall)
            bootstrapped_scores['F1-Score'].append(bs_f1)
            bootstrapped_scores['AUC'].append(bs_auc)

        # 计算均值和95%置信区间
        final_metrics = {}
        for metric_name, scores in bootstrapped_scores.items():
            if not scores:  # 如果所有抽样都失败了
                final_metrics[metric_name] = {'mean': 0, 'lower_ci': 0, 'upper_ci': 0}
                continue
            
            mean_score = np.mean(scores)
            lower_bound = np.percentile(scores, 2.5)
            upper_bound = np.percentile(scores, 97.5)
            final_metrics[metric_name] = {
                'mean': mean_score,
                'lower_ci': lower_bound,
                'upper_ci': upper_bound
            }
        
        self.logger.info("自助法性能评估完成")
        return final_metrics

    def _perform_discriminant_analysis(self, X_train, y_train, feature_cols):
        """执行详细的判别分析"""
        self.logger.info("执行详细判别分析...")

        analysis = {}

        # 1. 判别函数系数
        if hasattr(self.model, 'coef_'):
            coef = self.model.coef_[0] if self.model.coef_.ndim > 1 else self.model.coef_
            analysis['discriminant_coefficients'] = dict(zip(feature_cols, coef))

        # 2. 截距项
        if hasattr(self.model, 'intercept_'):
            analysis['intercept'] = self.model.intercept_[0] if hasattr(self.model.intercept_, '__len__') else self.model.intercept_

        # 3. 类别均值
        if hasattr(self.model, 'means_'):
            class_means = {}
            for i, class_name in enumerate(self.label_encoder.classes_):
                class_means[class_name] = dict(zip(feature_cols, self.model.means_[i]))
            analysis['class_means'] = class_means

        # 4. 先验概率
        if hasattr(self.model, 'priors_'):
            analysis['priors'] = dict(zip(self.label_encoder.classes_, self.model.priors_))

        # 5. 类别趋向性分析
        analysis['class_tendencies'] = self._analyze_class_tendencies(feature_cols)

        # 6. 判别方程构建
        analysis['discriminant_equation'] = self._build_discriminant_equation(feature_cols)

        self.logger.info("判别分析完成")
        return analysis

    def _analyze_class_tendencies(self, feature_cols):
        """分析两个类别的趋向性特征"""
        tendencies = {}

        if hasattr(self.model, 'coef_') and hasattr(self.model, 'means_'):
            coef = self.model.coef_[0] if self.model.coef_.ndim > 1 else self.model.coef_

            for i, feature in enumerate(feature_cols):
                coef_val = coef[i]
                class_means = [self.model.means_[j][i] for j in range(len(self.label_encoder.classes_))]

                # 分析系数方向和类别均值差异
                if coef_val > 0:
                    favored_class = self.label_encoder.classes_[1] if class_means[1] > class_means[0] else self.label_encoder.classes_[0]
                else:
                    favored_class = self.label_encoder.classes_[0] if class_means[1] > class_means[0] else self.label_encoder.classes_[1]

                tendencies[feature] = {
                    'coefficient': coef_val,
                    'class_means': dict(zip(self.label_encoder.classes_, class_means)),
                    'favored_class': favored_class,
                    'mean_difference': abs(class_means[1] - class_means[0])
                }

        return tendencies

    def _build_discriminant_equation(self, feature_cols):
        """构建判别方程"""
        equation_parts = []

        if hasattr(self.model, 'coef_') and hasattr(self.model, 'intercept_'):
            coef = self.model.coef_[0] if self.model.coef_.ndim > 1 else self.model.coef_
            intercept = self.model.intercept_[0] if hasattr(self.model.intercept_, '__len__') else self.model.intercept_

            # 构建线性判别函数
            equation = f"D(x) = {intercept:.4f}"
            for feature, coef_val in zip(feature_cols, coef):
                sign = "+" if coef_val >= 0 else ""
                equation += f" {sign}{coef_val:.4f}*{feature}"

            equation_parts.append(("Linear Discriminant Function", equation))
            equation_parts.append(("Decision Rule", "Classify as Class 1 if D(x) > 0, else Class 0"))

        return equation_parts

    def _find_optimal_threshold(self, y_true, y_decision):
        """寻找最优阈值用于验证集（基于约登指数）"""
        self.logger.info("寻找最优判别阈值 (基于约登指数)...")

        # 使用roc_curve计算TPR, FPR和阈值
        fpr, tpr, roc_thresholds = roc_curve(y_true, y_decision)
        
        # 计算约登指数 J = TPR - FPR
        youden_j = tpr - fpr
        
        # 找到约登指数最大时的索引
        optimal_idx = np.argmax(youden_j)
        
        # 获取最佳阈值和对应的约登指数
        best_threshold = roc_thresholds[optimal_idx]
        best_j_score = youden_j[optimal_idx]
        
        self.optimal_threshold = best_threshold
        self.logger.info(f"最优阈值: {best_threshold:.4f}, 约登指数: {best_j_score:.4f}")

        # 为了兼容旧的报告结构，可以模拟一个threshold_results
        threshold_analysis = [
            {'threshold': t, 'tpr': tp, 'fpr': fp, 'youden_j': tp - fp}
            for t, tp, fp in zip(roc_thresholds, tpr, fpr)
        ]

        return {
            'optimal_threshold': best_threshold,
            'optimal_youden_j': best_j_score,
            'threshold_analysis': threshold_analysis
        }

    def _extract_feature_importance(self, feature_cols):
        """提取特征重要性（基于判别系数的绝对值）"""
        if hasattr(self.model, 'coef_'):
            coef = self.model.coef_[0] if self.model.coef_.ndim > 1 else self.model.coef_
            importance = np.abs(coef)

            # 归一化到0-1范围
            importance_normalized = importance / np.sum(importance)

            feature_importance = dict(zip(feature_cols, importance_normalized))

            # 按重要性排序
            sorted_importance = dict(sorted(feature_importance.items(),
                                          key=lambda x: x[1], reverse=True))

            self.logger.info("特征重要性提取完成")
            return sorted_importance
        else:
            self.logger.warning("无法提取特征重要性")
            return {}

    def _log_training_summary(self, train_metrics, val_metrics, optimal_threshold):
        """输出训练摘要"""
        self.logger.info("=== 模型训练摘要 ===")
        self.logger.info("训练集性能:")
        for metric, value in train_metrics.items():
            self.logger.info(f"  {metric}: {value:.4f}")

        self.logger.info("验证集性能 (均值 [95% CI]):")
        for metric, values in val_metrics.items():
            self.logger.info(f"  {metric}: {values['mean']:.4f} [{values['lower_ci']:.4f}, {values['upper_ci']:.4f}]")

        self.logger.info(f"最优判别阈值: {optimal_threshold['optimal_threshold']:.4f}")
        self.logger.info(f"最优阈值下约登指数: {optimal_threshold['optimal_youden_j']:.4f}")

    def generate_comprehensive_visualizations(self, X_train, y_train, X_val, y_val, feature_cols):
        """生成专业可视化图表"""
        self.logger.info("=== 4. 生成专业可视化图表 ===")

        # 1. 混淆矩阵
        self._plot_confusion_matrix(y_train, y_val)

        # 2. 性能指标柱状图
        self._plot_performance_comparison()

        # 3. ROC曲线对比图（单指标 vs 组合指标）
        self._plot_roc_single_vs_combined(X_val, y_val, feature_cols)

        # 4. 特征重要性对比图
        self._plot_feature_importance()

        # 5. 1D投影分类结果可视化
        self._plot_1d_projection_results(X_train, y_train, X_val, y_val)

        # 6. 特征分布分析
        self._plot_feature_distributions(X_train, y_train, feature_cols)

        # 7. 决策边界可视化
        self._plot_decision_boundary_2d(X_train, y_train, X_val, y_val, feature_cols)

        # 8. 特征相关性矩阵热图
        self._plot_feature_correlation_heatmap(X_train, feature_cols)

        self.logger.info("所有可视化图表生成完成")

    def _plot_confusion_matrix(self, y_train, y_val):
        """1. 优化混淆矩阵 - 简洁清晰的设计"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 4))

        # 获取真实标签和预测结果
        y_train_true = y_train
        y_val_true = y_val
        y_train_pred = self.results['predictions']['y_pred_train']
        y_val_pred = self.results['predictions']['y_pred_val']

        # 计算混淆矩阵
        n_classes = len(self.label_encoder.classes_)
        labels = list(range(n_classes))
        
        cm_train = confusion_matrix(y_train_true, y_train_pred, labels=labels)
        cm_val = confusion_matrix(y_val_true, y_val_pred, labels=labels)

        def plot_clean_heatmap(ax, cm, title, y_true, y_pred):
            """绘制简洁的混淆矩阵热图"""
            # 计算百分比
            cm_percent = np.zeros_like(cm, dtype=float)
            for i in range(cm.shape[0]):
                row_sum = cm[i].sum()
                if row_sum > 0:
                    cm_percent[i] = cm[i] / row_sum * 100

            # 创建注释文本
            annot_text = np.empty_like(cm, dtype=object)
            for i in range(cm.shape[0]):
                for j in range(cm.shape[1]):
                    count = int(cm[i, j])
                    percent = cm_percent[i, j]
                    annot_text[i, j] = f'{count}\n({percent:.1f}%)'

            # 使用优化的配色方案
            cmap = sns.color_palette("Blues", as_cmap=True)
            
            # 绘制热图
            im = ax.imshow(cm, interpolation='nearest', cmap=cmap, aspect='equal')
            
            # 添加文本注释
            thresh = cm.max() / 2
            for i in range(cm.shape[0]):
                for j in range(cm.shape[1]):
                    text_color = 'white' if cm[i, j] > thresh else 'black'
                    ax.text(j, i, annot_text[i, j], ha='center', va='center',
                           color=text_color, fontsize=11, fontweight='bold')

            # 设置坐标轴
            ax.set_xticks(range(n_classes))
            ax.set_yticks(range(n_classes))
            ax.set_xticklabels(self.label_encoder.classes_, fontweight='bold')
            ax.set_yticklabels(self.label_encoder.classes_, fontweight='bold')
            
            ax.set_title(title, fontweight='bold', fontsize=13, pad=15)
            ax.set_xlabel('Predicted Label', fontweight='bold')
            ax.set_ylabel('True Label', fontweight='bold')

            # 计算关键统计指标
            total_samples = int(cm.sum())
            accuracy = np.trace(cm) / total_samples if total_samples > 0 else 0
            
            # 添加简洁的统计信息
            stats_text = f'Accuracy: {accuracy:.3f}\nSamples: {total_samples}'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                   fontsize=9, verticalalignment='top', fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', 
                           edgecolor='gray', alpha=0.9))

        # 绘制两个混淆矩阵
        plot_clean_heatmap(ax1, cm_train, 'Training Set', y_train_true, y_train_pred)
        plot_clean_heatmap(ax2, cm_val, 'Validation Set', y_val_true, y_val_pred)

        plt.tight_layout()
        self._save_plot(fig, 'confusion_matrix_optimized')

    def _plot_performance_comparison(self):
        """2. 性能指标柱状图（训练集vs验证集对比） - 优化配色版"""
        fig, ax = plt.subplots(figsize=(5, 3.5))

        metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'AUC']
        train_values = [self.results['train_metrics'].get(m, 0.0) for m in metrics]
        
        val_means = [self.results['val_metrics'][m]['mean'] for m in metrics]
        val_lower_ci = [self.results['val_metrics'][m]['lower_ci'] for m in metrics]
        val_upper_ci = [self.results['val_metrics'][m]['upper_ci'] for m in metrics]
        
        # 计算误差
        y_err = np.array([
            [mean - lower for mean, lower in zip(val_means, val_lower_ci)],
            [upper - mean for mean, upper in zip(val_means, val_upper_ci)]
        ])

        x = np.arange(len(metrics))
        width = 0.35

        # 使用学术期刊标准配色 - 蓝色和橙色
        bars1 = ax.bar(x - width/2, train_values, width, label='Training (Point Estimate)',
                      color=self.color_palette['primary'], alpha=0.9, 
                      edgecolor='black', linewidth=0.5)
        bars2 = ax.bar(x + width/2, val_means, width, label='Validation (Mean & 95% CI)',
                      color=self.color_palette['secondary'], alpha=0.9,
                      yerr=y_err, capsize=5, ecolor=self.color_palette['dark'],
                      edgecolor='black', linewidth=0.5)

        # 添加数值标签 - 改进字体大小和位置
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{height:.3f}', ha='center', va='bottom', 
                       fontsize=8, fontweight='bold')

        # 设置坐标轴
        ax.set_xlabel('Performance Metrics', fontweight='bold')
        ax.set_ylabel('Score', fontweight='bold')
        ax.set_title('LDA_Shrinkage: Performance Comparison', fontweight='bold', pad=15)
        ax.set_xticks(x)
        ax.set_xticklabels(metrics, fontweight='bold')
        ax.set_ylim(0, 1.15)

        # 优化图例
        ax.legend(fontsize=9, loc='upper right', framealpha=0.9)

        # 添加性能差异分析
        avg_train = np.mean(train_values)
        avg_val = np.mean(val_means)
        performance_gap = avg_train - avg_val
        
        gap_text = f'Avg Performance Gap: {performance_gap:.3f}'
        ax.text(0.02, 0.98, gap_text, transform=ax.transAxes, 
               fontsize=8, verticalalignment='top',
               bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

        plt.tight_layout()
        self._save_plot(fig, 'performance_comparison_enhanced')

    def _get_short_feature_name(self, feature_name):
        """从完整特征名中提取简称和分类学前缀"""
        parts = feature_name.split(';')
        if not parts:
            return feature_name
        
        last_part = parts[-1]
        
        # 提取分类单元前缀（如 p__, c__）和名称
        rank_match = last_part.split('__')
        if len(rank_match) == 2:
            rank, name = rank_match
            # 如果名称为空或未分类，则使用上一级
            if name.lower() in ['', 'unclassified'] and len(parts) > 1:
                 prev_part = parts[-2].split('__')
                 if len(prev_part) == 2:
                     return f"{prev_part[0]} {prev_part[1]}"
            return f"{rank} {name}"
        
        return last_part

    def _plot_roc_single_vs_combined(self, X_val, y_val, feature_cols):
        """3. ROC曲线对比图（单指标 vs 组合指标）"""
        fig, ax = plt.subplots(figsize=(4, 3.5))

        # 1. 绘制组合指标 (LDA模型)的ROC曲线
        if 'AUC' in self.results['val_metrics']:
            fpr_model, tpr_model, _ = roc_curve(y_val, self.results['predictions']['y_proba_val'][:, 1])
            auc_metrics = self.results['val_metrics']['AUC']
            label_text = (f"Combined Model (AUC = {auc_metrics['mean']:.3f} "
                          f"[{auc_metrics['lower_ci']:.3f}, {auc_metrics['upper_ci']:.3f}])")
            ax.plot(fpr_model, tpr_model, color=self.color_palette['success'], linewidth=2.5,
                   label=label_text, zorder=10)

        # 2. 绘制每个单一指标的ROC曲线
        # 使用色盲友好的定性调色板
        feature_palette = self.colorblind_safe['qualitative']
        
        for i, feature in enumerate(feature_cols):
            scores = X_val[feature]
            
            # 确保标签是二元的
            if len(np.unique(y_val)) < 2:
                self.logger.warning(f"跳过特征 {feature} 的ROC计算，因为标签不是二元的")
                continue

            auc_single = roc_auc_score(y_val, scores)
            # 如果AUC<0.5，说明特征和标签是负相关，翻转分数以获得有意义的AUC
            if auc_single < 0.5:
                 scores = -scores
                 auc_single = 1 - auc_single

            fpr_single, tpr_single, _ = roc_curve(y_val, scores)
            short_name = self._get_short_feature_name(feature)

            # 使用安全的局部调色板
            color = feature_palette[i % len(feature_palette)]
            ax.plot(fpr_single, tpr_single, linewidth=1, linestyle='--', color=color,
                   label=f'{short_name} (AUC = {auc_single:.3f})')

        # 3. 绘制对角线
        ax.plot([0, 1], [0, 1], 'k--', alpha=0.5, linewidth=1)

        ax.set_xlabel('False Positive Rate', fontweight='bold')
        ax.set_ylabel('True Positive Rate', fontweight='bold')
        ax.set_title('ROC Curves: Single vs. Combined', fontweight='bold')
        ax.legend(fontsize=6, loc='lower right', frameon=True, fancybox=True, framealpha=0.7, 
                 handlelength=1, handletextpad=0.3, columnspacing=0.5)

        plt.tight_layout()
        self._save_plot(fig, 'roc_single_vs_combined')

    def _save_plot(self, fig, name):
        """保存图表为PNG和PDF两种格式，优化PDF可编辑性"""
        # 保存PNG格式（用于预览）
        png_path = os.path.join(self.output_dir, 'plots', f"{name}.png")
        fig.savefig(png_path, dpi=300, bbox_inches='tight', pad_inches=0.05, 
                   facecolor='white', edgecolor='none')
        
        # 保存PDF格式（矢量格式，可编辑）
        pdf_path = os.path.join(self.output_dir, 'plots', f"{name}.pdf")
        fig.savefig(pdf_path, dpi=300, bbox_inches='tight', pad_inches=0.05,
                   format='pdf', facecolor='white', edgecolor='none',
                   transparent=False, metadata={'Creator': 'LDA_Shrinkage_Trainer',
                                               'Subject': 'Scientific Visualization',
                                               'Keywords': 'LDA, Machine Learning, Microbiome'})
        plt.close(fig)

    def _plot_feature_importance(self):
        """4. 特征重要性对比图（最重要的在顶部）"""
        if not self.results['feature_importance']:
            self.logger.warning("无特征重要性数据，跳过此图")
            return

        fig, ax = plt.subplots(figsize=(3.5, 3))

        # 获取特征重要性数据并按重要性排序
        importance_dict = self.results['feature_importance']
        # 按值（重要性）排序
        sorted_importance = sorted(importance_dict.items(), key=lambda x: x[1])
        features = [self._get_short_feature_name(item[0]) for item in sorted_importance]
        importances = [item[1] for item in sorted_importance]

        # 创建水平柱状图 - 使用渐变配色
        y_pos = np.arange(len(features))
        colors = plt.cm.viridis(np.linspace(0.3, 0.9, len(features)))
        bars = ax.barh(y_pos, importances, color=colors, alpha=0.9, edgecolor='black', linewidth=0.5)

        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax.text(width, bar.get_y() + bar.get_height()/2,
                   f' {width:.3f}', ha='left', va='center', fontsize=6)

        ax.set_yticks(y_pos)
        ax.set_yticklabels(features, fontsize=7)
        ax.set_xlabel('Normalized Importance', fontweight='bold')
        ax.set_title('LDA_Shrinkage: Feature Importance', fontweight='bold')
        ax.spines['bottom'].set_visible(False)
        ax.xaxis.set_ticks_position('top')
        ax.xaxis.set_label_position('top')

        plt.tight_layout()
        self._save_plot(fig, 'feature_importance_sorted')

    def _plot_1d_projection_results(self, X_train, y_train, X_val, y_val):
        """5. 训练集和验证集的1D投影分类结果可视化 - 解决重叠问题"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(5, 6))

        # 获取判别分数（1D投影）
        decision_train = self.results['predictions']['y_decision_train']
        decision_val = self.results['predictions']['y_decision_val']

        # 使用更清晰的配色方案
        colors = [self.color_palette['primary'], self.color_palette['secondary']]
        markers = ['o', 's']  # 圆形和方形标记

        # 训练集1D投影
        y_positions = {0: -0.15, 1: 0.15}
        for i, class_name in enumerate(self.label_encoder.classes_):
            mask = y_train == i
            scores = decision_train[mask]
            n_samples = np.sum(mask)
            
            if n_samples > 0:
                np.random.seed(42 + i)
                y_jitter = np.random.uniform(-0.05, 0.05, n_samples)
                y_coords = np.full(n_samples, y_positions[i]) + y_jitter
                
                ax1.scatter(scores, y_coords, c=colors[i], marker=markers[i],
                           alpha=0.8, s=35, edgecolors='black', linewidth=0.5,
                           label=f'{class_name} (n={n_samples})')

        # 添加决策边界线
        ax1.axvline(x=0, color=self.color_palette['neutral'], 
                   linestyle='--', linewidth=2, alpha=0.8, 
                   label='Decision Boundary')
        
        if self.results['optimal_threshold']:
            opt_thresh = self.results['optimal_threshold']['optimal_threshold']
            ax1.axvline(x=opt_thresh, color=self.color_palette['accent'], 
                       linestyle=':', linewidth=2, alpha=0.8,
                       label=f'Optimal Threshold ({opt_thresh:.3f})')

        ax1.set_xlabel('Discriminant Score', fontweight='bold')
        ax1.set_ylabel('Class Distribution', fontweight='bold')
        ax1.set_title('Training Set: 1D Projection Results', fontweight='bold', pad=15)
        ax1.set_ylim([-0.3, 0.3])
        ax1.set_yticks([y_positions[0], y_positions[1]])
        ax1.set_yticklabels(self.label_encoder.classes_, fontweight='bold')

        # 验证集1D投影
        for i, class_name in enumerate(self.label_encoder.classes_):
            mask = y_val == i
            scores = decision_val[mask]
            n_samples = np.sum(mask)
            
            if n_samples > 0:
                np.random.seed(42 + i)
                y_jitter = np.random.uniform(-0.05, 0.05, n_samples)
                y_coords = np.full(n_samples, y_positions[i]) + y_jitter
                
                ax2.scatter(scores, y_coords, c=colors[i], marker=markers[i],
                           alpha=0.8, s=35, edgecolors='black', linewidth=0.5,
                           label=f'{class_name} (n={n_samples})')

        # 添加决策边界线
        ax2.axvline(x=0, color=self.color_palette['neutral'], 
                   linestyle='--', linewidth=2, alpha=0.8, 
                   label='Decision Boundary')
        
        if self.results['optimal_threshold']:
            opt_thresh = self.results['optimal_threshold']['optimal_threshold']
            ax2.axvline(x=opt_thresh, color=self.color_palette['accent'], 
                       linestyle=':', linewidth=2, alpha=0.8,
                       label=f'Optimal Threshold ({opt_thresh:.3f})')

        ax2.set_xlabel('Discriminant Score', fontweight='bold')
        ax2.set_ylabel('Class Distribution', fontweight='bold')
        ax2.set_title('Validation Set: 1D Projection Results', fontweight='bold', pad=15)
        ax2.set_ylim([-0.3, 0.3])
        ax2.set_yticks([y_positions[0], y_positions[1]])
        ax2.set_yticklabels(self.label_encoder.classes_, fontweight='bold')

        plt.tight_layout(h_pad=3)
        self._save_plot(fig, '1d_projection_results_enhanced')


    def _plot_feature_distributions(self, X_train, y_train, feature_cols):
        """6. 特征分布分析 - 显示每个特征在不同类别中的分布"""
        self.logger.info("开始绘制特征分布...")
        
        n_features = len(feature_cols)
        n_cols = min(3, n_features)
        n_rows = (n_features + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(5*n_cols, 4*n_rows))
        if n_features == 1:
            axes = [axes]
        elif n_rows == 1:
            axes = axes.reshape(1, -1)
        
        colors = [self.color_palette['primary'], self.color_palette['secondary']]
        
        for idx, feature in enumerate(feature_cols):
            row = idx // n_cols
            col = idx % n_cols
            ax = axes[row, col] if n_rows > 1 else axes[col]
            
            # 获取特征数据
            feature_data = X_train[feature]
            
            # 为每个类别绘制分布
            for i, class_name in enumerate(self.label_encoder.classes_):
                mask = y_train == i
                class_data = feature_data[mask]
                
                # 绘制直方图
                ax.hist(class_data, bins=20, alpha=0.7, color=colors[i], 
                       label=f'{class_name} (n={np.sum(mask)})', density=True)
                
                # 添加均值线
                mean_val = class_data.mean()
                ax.axvline(mean_val, color=colors[i], linestyle='--', linewidth=2, alpha=0.8)
            
            # 设置标题和标签
            short_name = self._get_short_feature_name(feature)
            ax.set_title(f'{short_name}\nDistribution', fontweight='bold', fontsize=11)
            ax.set_xlabel('Feature Value (CLR-transformed)', fontweight='bold')
            ax.set_ylabel('Density', fontweight='bold')
            ax.legend(fontsize=9)
            
            # 添加统计信息
            class_0_data = feature_data[y_train == 0]
            class_1_data = feature_data[y_train == 1]
            
            # 计算效应大小 (Cohen's d)
            pooled_std = np.sqrt(((len(class_0_data) - 1) * class_0_data.var() + 
                                 (len(class_1_data) - 1) * class_1_data.var()) / 
                                (len(class_0_data) + len(class_1_data) - 2))
            cohens_d = abs(class_0_data.mean() - class_1_data.mean()) / pooled_std
            
            effect_text = f"Cohen's d: {cohens_d:.3f}"
            ax.text(0.02, 0.98, effect_text, transform=ax.transAxes, 
                   fontsize=8, verticalalignment='top',
                   bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
        
        # 隐藏多余的子图
        for idx in range(n_features, n_rows * n_cols):
            row = idx // n_cols
            col = idx % n_cols
            if n_rows > 1:
                axes[row, col].set_visible(False)
            else:
                axes[col].set_visible(False)

        plt.tight_layout()
        self._save_plot(fig, 'feature_distributions_analysis')

    def _plot_decision_boundary_2d(self, X_train, y_train, X_val, y_val, feature_cols):
        """8. 2D决策边界可视化 - 使用前两个最重要的特征"""
        self.logger.info("开始绘制2D决策边界...")
        
        if len(feature_cols) < 2:
            self.logger.warning("特征数量少于2个，跳过决策边界可视化")
            return
        
        # 获取最重要的两个特征
        feature_importance = self.results['feature_importance']
        top_2_features = list(feature_importance.keys())[:2]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        def plot_decision_boundary(ax, X_data, y_data, title, dataset_type):
            # 提取前两个特征
            X_2d = X_data[top_2_features].values
            
            # 创建网格
            h = 0.02  # 网格步长
            x_min, x_max = X_2d[:, 0].min() - 1, X_2d[:, 0].max() + 1
            y_min, y_max = X_2d[:, 1].min() - 1, X_2d[:, 1].max() + 1
            xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                                np.arange(y_min, y_max, h))
            
            # 创建用于预测的数据点
            mesh_points = np.c_[xx.ravel(), yy.ravel()]
            
            # 为网格点创建完整的特征向量（其他特征用均值填充）
            full_mesh = np.zeros((mesh_points.shape[0], len(feature_cols)))
            for i, feature in enumerate(feature_cols):
                if feature in top_2_features:
                    feature_idx = top_2_features.index(feature)
                    full_mesh[:, i] = mesh_points[:, feature_idx]
                else:
                    full_mesh[:, i] = X_data[feature].mean()
            
            # 转换为DataFrame以保持特征名
            mesh_df = pd.DataFrame(full_mesh, columns=feature_cols)
            
            # 预测网格点
            Z = self.model.predict(mesh_df)
            Z = Z.reshape(xx.shape)
            
            # 绘制决策边界
            ax.contourf(xx, yy, Z, alpha=0.4, cmap=plt.cm.RdYlBu, levels=1)
            ax.contour(xx, yy, Z, colors='black', linestyles='--', linewidths=1)
            
            # 绘制数据点
            colors = [self.color_palette['primary'], self.color_palette['secondary']]
            markers = ['o', 's']
            
            for i, class_name in enumerate(self.label_encoder.classes_):
                mask = y_data == i
                class_data = X_2d[mask]
                ax.scatter(class_data[:, 0], class_data[:, 1], 
                          c=colors[i], marker=markers[i], s=50, alpha=0.8,
                          edgecolors='black', linewidth=0.5,
                          label=f'{class_name} (n={np.sum(mask)})')
            
            # 设置标签和标题
            feature1_short = self._get_short_feature_name(top_2_features[0])
            feature2_short = self._get_short_feature_name(top_2_features[1])
            
            ax.set_xlabel(f'{feature1_short}', fontweight='bold')
            ax.set_ylabel(f'{feature2_short}', fontweight='bold')
            ax.set_title(f'{title}\nDecision Boundary (Top 2 Features)', fontweight='bold', pad=15)
            ax.legend(fontsize=9, framealpha=0.9)
        
        # 绘制训练集和验证集的决策边界
        plot_decision_boundary(ax1, X_train, y_train, 'Training Set', 'train')
        plot_decision_boundary(ax2, X_val, y_val, 'Validation Set', 'val')
        
        plt.tight_layout()
        self._save_plot(fig, 'decision_boundary_2d')

    def _plot_feature_correlation_heatmap(self, X_train, feature_cols):
        """9. 增强特征相关性矩阵热图 - 包含统计显著性和网络分析"""
        fig = plt.figure(figsize=(14, 6))
        gs = fig.add_gridspec(1, 2, width_ratios=[2, 1])
        
        ax1 = fig.add_subplot(gs[0])
        ax2 = fig.add_subplot(gs[1])

        corr_matrix = X_train.corr(method='pearson')
        
        # 使用优化的配色方案
        cmap = sns.diverging_palette(250, 10, as_cmap=True)
        
        # 绘制相关性热图
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))  # 只显示下三角
        heatmap_obj = sns.heatmap(corr_matrix, mask=mask, annot=False, cmap=cmap, center=0,
                    square=True, ax=ax1, cbar_kws={'shrink': 0.8, 'label': 'Pearson Correlation'},
                    linewidths=0.5, linecolor='white')

        # 手动添加注释和显著性标记
        for i in range(len(feature_cols)):
            for j in range(len(feature_cols)):
                if i > j:  # 下三角显示相关系数
                    corr_val = corr_matrix.iloc[i, j]
                    _, p_val = pearsonr(X_train.iloc[:, i], X_train.iloc[:, j])
                    
                    # 根据相关性强度选择颜色
                    text_color = 'white' if abs(corr_val) > 0.5 else 'black'
                    
                    # 显示相关系数
                    ax1.text(j + 0.5, i + 0.5, f'{corr_val:.2f}', 
                            ha='center', va='center', color=text_color, 
                            fontsize=10, fontweight='bold')
                    
                    # 添加显著性星号
                    if p_val < 0.001:
                        stars = '***'
                    elif p_val < 0.01:
                        stars = '**'
                    elif p_val < 0.05:
                        stars = '*'
                    else:
                        stars = ''
                    
                    if stars:
                        ax1.text(j + 0.5, i + 0.3, stars, ha='center', va='center', 
                                color=self.color_palette['warning'], fontsize=8, fontweight='bold')

        ax1.set_title('Feature Correlation Matrix\n(Pearson Coefficients & Significance)', 
                     fontweight='bold', pad=20)
        
        simplified_labels = [self._get_short_feature_name(f) for f in feature_cols]
        ax1.set_xticklabels(simplified_labels, rotation=45, ha='right', fontweight='bold')
        ax1.set_yticklabels(simplified_labels, rotation=0, fontweight='bold')

        # 相关性强度分布图
        corr_values = corr_matrix.values[np.triu_indices_from(corr_matrix.values, k=1)]
        
        ax2.hist(corr_values, bins=15, alpha=0.7, color=self.color_palette['info'], 
                edgecolor='black', linewidth=0.5)
        ax2.axvline(np.mean(corr_values), color=self.color_palette['success'], 
                   linestyle='--', linewidth=2, label=f'Mean: {np.mean(corr_values):.3f}')
        ax2.axvline(np.median(corr_values), color=self.color_palette['warning'], 
                   linestyle=':', linewidth=2, label=f'Median: {np.median(corr_values):.3f}')
        
        ax2.set_xlabel('Correlation Coefficient', fontweight='bold')
        ax2.set_ylabel('Frequency', fontweight='bold')
        ax2.set_title('Correlation Distribution', fontweight='bold', pad=15)
        ax2.legend(fontsize=9, framealpha=0.9)
        
        # 添加统计信息
        high_corr = np.sum(np.abs(corr_values) > 0.7)
        moderate_corr = np.sum((np.abs(corr_values) > 0.3) & (np.abs(corr_values) <= 0.7))
        low_corr = np.sum(np.abs(corr_values) <= 0.3)
        
        stats_text = f'High (>0.7): {high_corr}\nModerate (0.3-0.7): {moderate_corr}\nLow (≤0.3): {low_corr}'
        ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, 
                fontsize=9, verticalalignment='top',
                bbox=dict(boxstyle='round,pad=0.3', facecolor=self.color_palette['light'], alpha=0.9))

        plt.tight_layout()
        self._save_plot(fig, 'feature_correlation_comprehensive')

    def save_comprehensive_results(self, feature_cols):
        """保存判别方程、详细解释和性能指标的文本数据"""
        self.logger.info("=== 5. 保存结构化结果 ===")

        # 1. 保存详细的判别分析报告
        self._save_discriminant_analysis_report(feature_cols)

        # 2. 保存性能指标详细报告
        self._save_performance_metrics_report()

        # 3. 保存类别趋向性分析
        self._save_class_tendency_analysis()

        # 4. 保存最优阈值分析
        self._save_optimal_threshold_analysis()

        # 5. 保存模型
        model_file = os.path.join(self.output_dir, 'models', 'lda_shrinkage_model.pkl')
        joblib.dump(self.model, model_file)

        # 6. 保存预测结果
        self._save_prediction_results()

        self.logger.info("所有结果已保存至结构化文件")

    def _save_discriminant_analysis_report(self, feature_cols):
        """保存判别方程和详细解释"""
        report_file = os.path.join(self.output_dir, 'analysis', 'discriminant_analysis_report.txt')

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=== LDA_Shrinkage 判别分析详细报告 ===\n\n")
            f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"模型类型: {self.model_name}\n")
            f.write(f"收缩参数: {self.model.shrinkage}\n\n")

            # 判别方程
            if 'discriminant_equation' in self.results['discriminant_analysis']:
                f.write("=== 判别方程 ===\n")
                for title, equation in self.results['discriminant_analysis']['discriminant_equation']:
                    f.write(f"{title}:\n{equation}\n\n")

            # 判别系数详细解释
            if 'discriminant_coefficients' in self.results['discriminant_analysis']:
                f.write("=== 判别系数详细解释 ===\n")
                coeffs = self.results['discriminant_analysis']['discriminant_coefficients']
                for feature, coef in coeffs.items():
                    f.write(f"{feature}: {coef:.6f}\n")
                    if coef > 0:
                        f.write(f"  -> 正系数，有利于类别 {self.label_encoder.classes_[1]}\n")
                    else:
                        f.write(f"  -> 负系数，有利于类别 {self.label_encoder.classes_[0]}\n")
                f.write("\n")

            # 类别均值
            if 'class_means' in self.results['discriminant_analysis']:
                f.write("=== 类别均值分析 ===\n")
                class_means = self.results['discriminant_analysis']['class_means']
                for class_name, means in class_means.items():
                    f.write(f"{class_name} 类别均值:\n")
                    for feature, mean_val in means.items():
                        f.write(f"  {feature}: {mean_val:.6f}\n")
                    f.write("\n")

            # 先验概率
            if 'priors' in self.results['discriminant_analysis']:
                f.write("=== 先验概率 ===\n")
                priors = self.results['discriminant_analysis']['priors']
                for class_name, prior in priors.items():
                    f.write(f"{class_name}: {prior:.4f} ({prior*100:.1f}%)\n")
                f.write("\n")

            # 判定流程详细解释
            f.write("=== 判定流程详细解释 ===\n")
            f.write("1. 数据预处理:\n")
            f.write("   - 对输入特征进行CLR转换\n")
            f.write("   - 零值使用乘性替换方法处理\n\n")
            f.write("2. 判别分数计算:\n")
            f.write("   - 使用线性判别函数计算判别分数\n")
            f.write("   - D(x) = w₀ + w₁x₁ + w₂x₂ + ... + wₙxₙ\n\n")
            f.write("3. 分类决策:\n")
            f.write("   - 如果 D(x) > 阈值，分类为类别1\n")
            f.write("   - 如果 D(x) ≤ 阈值，分类为类别0\n")
            f.write(f"   - 默认阈值: 0.0\n")
            if self.results['optimal_threshold']:
                f.write(f"   - 最优阈值: {self.results['optimal_threshold']['optimal_threshold']:.6f}\n")
            f.write("\n")

    def _save_performance_metrics_report(self):
        """保存性能指标详细报告"""
        metrics_file = os.path.join(self.output_dir, 'results', 'performance_metrics_detailed.txt')

        with open(metrics_file, 'w', encoding='utf-8') as f:
            f.write("=== LDA_Shrinkage 性能指标详细报告 ===\n\n")

            f.write("训练集性能指标:\n")
            for metric, value in self.results['train_metrics'].items():
                f.write(f"  {metric}: {value:.6f}\n")

            f.write("\n验证集性能指标 (均值与95%置信区间):\n")
            for metric, values in self.results['val_metrics'].items():
                f.write(f"  {metric}: {values['mean']:.6f} (95% CI: [{values['lower_ci']:.6f}, {values['upper_ci']:.6f}])\n")

            # 过拟合分析
            f.write("\n=== 过拟合分析 ===\n")
            train_acc = self.results['train_metrics']['Accuracy']
            val_acc_mean = self.results['val_metrics']['Accuracy']['mean']
            overfitting_gap = train_acc - val_acc_mean

            f.write(f"训练集准确率: {train_acc:.6f}\n")
            f.write(f"验证集准确率 (均值): {val_acc_mean:.6f}\n")
            f.write(f"过拟合差距: {overfitting_gap:.6f}\n")

            if overfitting_gap < 0.05:
                f.write("评估: 模型泛化能力良好，无明显过拟合\n")
            elif overfitting_gap < 0.1:
                f.write("评估: 模型存在轻微过拟合\n")
            else:
                f.write("评估: 模型存在明显过拟合，建议增加正则化\n")

    def _save_class_tendency_analysis(self):
        """保存两个类别的趋向性特征分析"""
        tendency_file = os.path.join(self.output_dir, 'analysis', 'class_tendency_analysis.txt')

        with open(tendency_file, 'w', encoding='utf-8') as f:
            f.write("=== 两个类别的趋向性特征分析 ===\n\n")

            if 'class_tendencies' in self.results['discriminant_analysis']:
                tendencies = self.results['discriminant_analysis']['class_tendencies']

                # 按均值差异排序
                sorted_features = sorted(tendencies.items(),
                                       key=lambda x: x[1]['mean_difference'], reverse=True)

                f.write("特征按类别差异排序（从大到小）:\n\n")

                for feature, info in sorted_features:
                    f.write(f"特征: {feature}\n")
                    f.write(f"  判别系数: {info['coefficient']:.6f}\n")
                    f.write(f"  有利类别: {info['favored_class']}\n")
                    f.write(f"  类别均值差异: {info['mean_difference']:.6f}\n")

                    # 详细的类别均值
                    f.write("  各类别均值:\n")
                    for class_name, mean_val in info['class_means'].items():
                        f.write(f"    {class_name}: {mean_val:.6f}\n")

                    # 生物学解释建议
                    if info['coefficient'] > 0:
                        f.write(f"  解释: 该特征值越高，越倾向于分类为 {self.label_encoder.classes_[1]}\n")
                    else:
                        f.write(f"  解释: 该特征值越高，越倾向于分类为 {self.label_encoder.classes_[0]}\n")
                    f.write("\n")

    def _save_optimal_threshold_analysis(self):
        """保存最优阈值分析"""
        threshold_file = os.path.join(self.output_dir, 'analysis', 'optimal_threshold_analysis.txt')

        with open(threshold_file, 'w', encoding='utf-8') as f:
            f.write("=== 最优阈值分析报告 ===\n\n")

            if self.results['optimal_threshold']:
                opt_info = self.results['optimal_threshold']

                f.write(f"最优阈值: {opt_info['optimal_threshold']:.6f}\n")
                f.write(f"最优阈值下约登指数: {opt_info['optimal_youden_j']:.6f}\n\n")

                f.write("阈值选择说明:\n")
                f.write("- 通过在验证集上计算ROC曲线，找到使约登指数(J = TPR - FPR)最大化的点\n")
                f.write("- 优化目标: 最大化约登指数 (Youden's J Statistic)\n")
                f.write("- 默认阈值: 0.0（LDA标准决策边界）\n\n")

                f.write("使用建议:\n")
                f.write("1. 约登指数是衡量诊断试验准确性的常用方法，能同时反映敏感性和特异性。\n")
                f.write("2. 对于不平衡数据集，约登指数通常比F1分数更稳定可靠。\n")
                f.write("3. 根据实际应用场景调整阈值以平衡精确率和召回率\n")

    def _save_prediction_results(self):
        """保存预测结果"""
        # 训练集预测结果
        train_pred_df = pd.DataFrame({
            'Sample_Index': range(len(self.results['predictions']['y_pred_train'])),
            'True_Label_Encoded': self.results['predictions']['y_pred_train'],  # 注意：这里需要真实标签
            'Predicted_Label_Encoded': self.results['predictions']['y_pred_train'],
            'Probability_Class_0': self.results['predictions']['y_proba_train'][:, 0],
            'Probability_Class_1': self.results['predictions']['y_proba_train'][:, 1],
            'Discriminant_Score': self.results['predictions']['y_decision_train']
        })
        train_pred_file = os.path.join(self.output_dir, 'results', 'train_predictions.csv')
        train_pred_df.to_csv(train_pred_file, index=False)

        # 验证集预测结果
        val_pred_df = pd.DataFrame({
            'Sample_Index': range(len(self.results['predictions']['y_pred_val'])),
            'True_Label_Encoded': self.results['predictions']['y_pred_val'],  # 注意：这里需要真实标签
            'Predicted_Label_Encoded': self.results['predictions']['y_pred_val'],
            'Probability_Class_0': self.results['predictions']['y_proba_val'][:, 0],
            'Probability_Class_1': self.results['predictions']['y_proba_val'][:, 1],
            'Discriminant_Score': self.results['predictions']['y_decision_val']
        })
        val_pred_file = os.path.join(self.output_dir, 'results', 'val_predictions.csv')
        val_pred_df.to_csv(val_pred_file, index=False)

    def save_visualization_data(self, X_train, y_train, X_val, y_val, feature_cols):
        """将所有用于可视化的核心数据保存到文件"""
        self.logger.info("=== 6. 保存可视化数据 ===")
        data_dir = os.path.join(self.output_dir, 'visualization_data')

        # 1. 混淆矩阵数据
        cm_train = confusion_matrix(y_train, self.results['predictions']['y_pred_train'])
        cm_val = confusion_matrix(y_val, self.results['predictions']['y_pred_val'])
        pd.DataFrame(cm_train, columns=self.label_encoder.classes_, index=self.label_encoder.classes_).to_csv(os.path.join(data_dir, 'confusion_matrix_train.csv'))
        pd.DataFrame(cm_val, columns=self.label_encoder.classes_, index=self.label_encoder.classes_).to_csv(os.path.join(data_dir, 'confusion_matrix_val.csv'))

        # 2. 性能指标数据
        perf_df = pd.DataFrame([self.results['train_metrics'], self.results['val_metrics']], index=['Training', 'Validation'])
        perf_df.to_csv(os.path.join(data_dir, 'performance_metrics.csv'))

        # 3. ROC曲线数据
        # 组合模型
        fpr, tpr, _ = roc_curve(y_val, self.results['predictions']['y_proba_val'][:, 1])
        roc_combined_df = pd.DataFrame({'fpr': fpr, 'tpr': tpr})
        roc_combined_df.to_csv(os.path.join(data_dir, 'roc_curve_combined_model.csv'), index=False)
        # 单一特征
        all_roc_single = []
        for feature in feature_cols:
            scores = X_val[feature]
            auc_single = roc_auc_score(y_val, scores)
            if auc_single < 0.5:
                scores = -scores
            fpr_s, tpr_s, _ = roc_curve(y_val, scores)
            roc_single_df = pd.DataFrame({'fpr': fpr_s, 'tpr': tpr_s, 'feature': feature})
            all_roc_single.append(roc_single_df)
        pd.concat(all_roc_single).to_csv(os.path.join(data_dir, 'roc_curves_single_features.csv'), index=False)

        # 4. 特征重要性数据
        importance_df = pd.DataFrame(list(self.results['feature_importance'].items()), columns=['Feature', 'Importance'])
        importance_df.to_csv(os.path.join(data_dir, 'feature_importance.csv'), index=False)

        # 5. 1D投影数据
        proj_train_df = pd.DataFrame({
            'discriminant_score': self.results['predictions']['y_decision_train'],
            'true_label': self.label_encoder.inverse_transform(y_train)
        })
        proj_val_df = pd.DataFrame({
            'discriminant_score': self.results['predictions']['y_decision_val'],
            'true_label': self.label_encoder.inverse_transform(y_val)
        })
        proj_train_df.to_csv(os.path.join(data_dir, '1d_projection_train.csv'), index=False)
        proj_val_df.to_csv(os.path.join(data_dir, '1d_projection_val.csv'), index=False)

        # 6. 特征分布数据
        dist_df = X_train.copy()
        dist_df['Group'] = self.label_encoder.inverse_transform(y_train)
        dist_df.to_csv(os.path.join(data_dir, 'feature_distributions.csv'), index=False)

        # 7. 决策边界数据
        top_2_features = list(self.results['feature_importance'].keys())[:2]
        if len(top_2_features) == 2:
            boundary_train_df = X_train[top_2_features].copy()
            boundary_train_df['Group'] = self.label_encoder.inverse_transform(y_train)
            boundary_train_df.to_csv(os.path.join(data_dir, 'decision_boundary_train_data.csv'), index=False)
            
            boundary_val_df = X_val[top_2_features].copy()
            boundary_val_df['Group'] = self.label_encoder.inverse_transform(y_val)
            boundary_val_df.to_csv(os.path.join(data_dir, 'decision_boundary_val_data.csv'), index=False)

        # 8. 特征相关性数据
        corr_matrix = X_train.corr(method='pearson')
        corr_matrix.to_csv(os.path.join(data_dir, 'feature_correlation_matrix.csv'))

        self.logger.info("所有可视化数据已保存")

    def run_complete_training(self):
        """执行完整的LDA_Shrinkage训练流程"""
        try:
            self.logger.info("=== 开始LDA_Shrinkage完整训练 ===")

            # 1. 数据加载
            full_df = self.load_data()

            # 2. 数据预处理
            X_full, y_full, feature_cols = self.preprocess_data(full_df)

            # 3. 模型训练与分析（使用交叉验证）
            results = self.train_lda_model_cv(X_full, y_full, feature_cols)

            # 4. 从结果中提取最后一个fold的数据用于可视化
            last_fold_data = results['last_fold_data']
            X_train_viz = last_fold_data['X_train_orig']  # 使用原始特征数据进行可视化
            y_train_viz = last_fold_data['y_train']
            X_val_viz = last_fold_data['X_val_orig']
            y_val_viz = last_fold_data['y_val']

            # 5. 生成可视化
            self.generate_comprehensive_visualizations(X_train_viz, y_train_viz, X_val_viz, y_val_viz, feature_cols)

            # 6. 保存结果
            self.save_comprehensive_results(feature_cols)

            # 7. 保存可视化数据
            self.save_visualization_data(X_train_viz, y_train_viz, X_val_viz, y_val_viz, feature_cols)

            self.logger.info("=== LDA_Shrinkage完整训练完成 ===")
            self.logger.info(f"结果保存至: {self.output_dir}")

        except Exception as e:
            self.logger.error(f"训练过程中发生错误: {e}", exc_info=True)
            raise

if __name__ == "__main__":
    trainer = LDAShrinkageTrainer()
    trainer.run_complete_training()
