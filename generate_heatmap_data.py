# -*- coding: utf-8 -*-
"""
generate_heatmap_data.py

本脚本用于为差异热图生成预处理数据。
功能：
1.  读取多个分类学水平的特征丰度表。
2.  在每个分类学水平上，筛选出平均丰度排名前10的特征。
3.  对这Top 10特征的丰度数据进行Z-score标准化。
4.  将每个水平处理好的数据保存为独立的CSV文件，用于后续的热图可视化。

使用前请确保已安装必要的库:
pip install pandas numpy
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# --- 日志记录设置 ---
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S')

class HeatmapDataGenerator:
    """
    一个用于筛选Top特征并进行Z-score标准化以生成热图数据的类。
    """
    def __init__(self, input_files_dict, top_n=10, output_base_dir=None):
        """
        初始化生成器。
        
        参数:
        - input_files_dict (dict): 键为分类学水平，值为文件路径的字典。
        - top_n (int): 要筛选的顶部特征数量。
        - output_base_dir (str, optional): 输出的根目录。默认为用户桌面。
        """
        self.input_files = input_files_dict
        self.top_n = top_n
        if output_base_dir is None:
            output_base_dir = os.path.join(os.path.expanduser('~'), 'Desktop')
        
        timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
        self.output_dir = os.path.join(output_base_dir, f"Differential_Heatmap_Data_{timestamp}")
        
        self.sample_id_col = "SampleID"
        self.group_col = "Group"
        
        os.makedirs(self.output_dir, exist_ok=True)
        logging.info(f"所有输出文件将保存在: {self.output_dir}")

    def process_level(self, level_name, file_path):
        """
        处理单个分类学水平的文件。
        
        参数:
        - level_name (str): 分类学水平的名称 (e.g., 'phylum').
        - file_path (str): 输入文件的路径。
        """
        logging.info(f"--- 开始处理: {level_name} 水平 ---")
        if not os.path.exists(file_path):
            logging.error(f"文件未找到，跳过: {file_path}")
            return
        
        try:
            data_df = pd.read_csv(file_path, sep='\t', header=0)
            logging.info(f"成功加载文件: {os.path.basename(file_path)}")
            
            if self.sample_id_col not in data_df.columns or self.group_col not in data_df.columns:
                logging.error(f"文件 {file_path} 中缺少 '{self.sample_id_col}' 或 '{self.group_col}' 列。")
                return

            # 1. 识别特征列并计算平均丰度
            feature_cols = data_df.columns.drop([self.sample_id_col, self.group_col])
            feature_matrix = data_df[feature_cols]
            mean_abundance = feature_matrix.mean().sort_values(ascending=False)
            
            # 2. 筛选Top N特征
            num_features_to_select = min(self.top_n, len(mean_abundance))
            if num_features_to_select == 0:
                logging.warning(f"在 {level_name} 水平未找到任何特征数据，跳过。")
                return

            top_features = mean_abundance.head(num_features_to_select).index.tolist()
            logging.info(f"在 {level_name} 水平筛选出 Top {num_features_to_select} 特征。")
            
            # 3. 提取Top N特征的丰度数据
            top_features_df = feature_matrix[top_features]

            # 4. 对Top N特征进行Z-score标准化（按列/特征进行）
            # (x - mean) / std
            # 为避免除以零（如果一个特征在所有样本中值相同），我们将标准差为0的情况处理为Z-score=0
            zscore_df = top_features_df.apply(lambda x: (x - x.mean()) / x.std() if x.std() > 0 else 0, axis=0)
            
            # 5. 合并样本信息和标准化后的数据
            sample_info = data_df[[self.sample_id_col, self.group_col]]
            final_df = pd.concat([sample_info, zscore_df], axis=1)
            
            # 6. 保存结果
            output_filename = os.path.join(self.output_dir, f"heatmap_data_{level_name}.csv")
            final_df.to_csv(output_filename, index=False)
            logging.info(f"已保存用于热图的数据到: {output_filename}")

        except Exception as e:
            logging.error(f"处理文件 {file_path} 时发生错误: {e}")

    def run(self):
        """
        执行整个热图数据生成流程。
        """
        logging.info("======= 开始生成差异热图数据 =======")
        for level, path in self.input_files.items():
            self.process_level(level, path)
        logging.info("======= 差异热图数据生成全部完成 =======")


if __name__ == "__main__":
    # --- 输入文件配置 (与alpha_diversity_pipeline.py保持一致) ---
    input_file_paths = {
      'phylum': 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y门.txt',
      'class': 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y纲.txt',
      'order': 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y目.txt',
      'family': 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y科.txt',
      'genus': 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y属.txt',
      'species': 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y种.txt'
    }
    
    try:
        generator = HeatmapDataGenerator(input_files_dict=input_file_paths)
        generator.run()
    except Exception as e:
        logging.critical(f"脚本执行过程中遇到致命错误: {e}")
