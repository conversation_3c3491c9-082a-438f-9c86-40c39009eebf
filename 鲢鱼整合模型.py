from joblib import dump, load
import os
import traceback
import pandas as pd
import numpy as np
from sklearn.ensemble import VotingClassifier, RandomForestClassifier
from sklearn.model_selection import GridSearchCV, cross_validate, StratifiedKFold, learning_curve, LeaveOneOut, RepeatedStratifiedKFold
from sklearn.linear_model import LogisticRegression
import lightgbm as lgb
from sklearn.metrics import (make_scorer, f1_score, accuracy_score, precision_score, recall_score, 
                            roc_curve, auc, confusion_matrix, classification_report)
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.pipeline import Pipeline
import json
from datetime import datetime
import argparse
import pkg_resources
import sys
import platform
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

import seaborn as sns
from matplotlib.backends.backend_pdf import PdfPages

from scipy.optimize import differential_evolution
from scipy import stats
from scipy.stats import gmean
import warnings
warnings.filterwarnings('ignore')

import shap

# --- 常量定义 ---
CV_SPLITS = 10
CV_REPEATS = 5
N_JOBS = -1  # 使用所有可用CPU核心
RANDOM_STATE = 42
CLASS_NAMES = ['养殖', '野生']
TOP_N_FEATURES = 20 # SHAP图中显示的特征数量

# --- 英文化翻译映射 ---
SPECIES_MAP = {
    '鳙鱼': 'Bighead Carp',
    '鲢鱼': 'Silver Carp'
}
CLASS_LABEL_MAP = {
    '养殖': 'Culture',
    '野生': 'Wild'
}
# ---------------------

# 动态导入TabPFN
try:
    import torch
except ImportError:
    print("torch模块未安装，将进行安装...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "--no-cache-dir", "torch"])
    import torch

try:
    from tabpfn import TabPFNClassifier
except ImportError:
    print("tabpfn模块未安装，将进行安装...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "--no-cache-dir", "tabpfn"])
    from tabpfn import TabPFNClassifier

# 检测CUDA是否可用
def check_cuda_availability():
    """检查CUDA是否可用并返回适当的设备类型"""
    if torch.cuda.is_available():
        print(f"检测到CUDA，使用GPU: {torch.cuda.get_device_name(0)}")
        torch.cuda.empty_cache()
        return "cuda"
    else:
        print("未检测到CUDA，使用CPU")
        return "cpu"

# CLR转换函数
def clr_transform(data: pd.DataFrame, pseudo_count=1e-6) -> np.ndarray:
    """对DataFrame格式的特征数据应用中心对数比（CLR）转换。"""
    print("应用CLR转换...")
    if not isinstance(data, pd.DataFrame):
        raise TypeError("CLR转换的输入必须是Pandas DataFrame。")
    
    # 确保只处理数值列
    numeric_data = data.select_dtypes(include=[np.number])
    if numeric_data.empty:
        raise ValueError("数据中没有数值列可以进行CLR转换")
    
    print(f"CLR转换处理 {numeric_data.shape[1]} 个数值特征")
    
    # 检查是否有负值
    if (numeric_data < 0).any().any():
        print("警告: 数据中包含负值，将使用绝对值进行CLR转换")
        numeric_data = numeric_data.abs()
    
    # 检查是否有零值
    zero_count = (numeric_data == 0).sum().sum()
    if zero_count > 0:
        print(f"发现 {zero_count} 个零值，将使用伪计数 {pseudo_count} 进行处理")
    
    data_with_pseudo = numeric_data.values + pseudo_count
    geometric_mean = gmean(data_with_pseudo, axis=1)
    clr_data = np.log(data_with_pseudo / geometric_mean[:, np.newaxis])
    print("CLR转换完成。")
    return clr_data

# 设置字体
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman']
plt.rcParams['axes.unicode_minus'] = True 
plt.rcParams['pdf.fonttype'] = 42
plt.rcParams['svg.fonttype'] = 'none'

# 确保目录存在的辅助函数
def ensure_dir_exists(file_path: str):
    """确保文件路径的目录存在"""
    directory = os.path.dirname(file_path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)


# 核心训练与评估函数
def train_and_evaluate_models(train_path, val_path, test_path, output_dir, fish_type):
    """
    重构后的主函数，负责加载数据、训练模型、评估和生成可视化。
    使用训练集和验证集进行分析。
    """
    eng_species_name = SPECIES_MAP.get(fish_type, fish_type)
    
    # 1. 创建输出目录
    visualization_dir = os.path.join(output_dir, 'visualization', eng_species_name)
    model_dir = os.path.join(output_dir, 'models', eng_species_name)
    ensure_dir_exists(os.path.join(visualization_dir, 'dummy.txt'))
    ensure_dir_exists(os.path.join(model_dir, 'dummy.txt'))
    
    # 2. 加载用户提供的数据集
    print("加载预划分的数据集...")
    try:
        train_df = pd.read_csv(train_path)
        val_df = pd.read_csv(val_path)
        print(f"训练集: {train_df.shape}, 验证集: {val_df.shape}")
    except FileNotFoundError as e:
        print(f"错误：数据文件未找到 - {e}", file=sys.stderr)
        return
        
    # 3. 数据准备和预处理
    print("准备数据...")
    
    # 检查数据列结构
    print(f"训练集列名: {train_df.columns.tolist()}")
    
    # 确定标签列名
    label_col = None
    possible_label_cols = ['Group', 'Label', 'group', 'label', 'target', 'Target']
    for col in possible_label_cols:
        if col in train_df.columns:
            label_col = col
            break
    
    if label_col is None:
        print("错误：未找到标签列，请确保数据包含以下列名之一：Group, Label, group, label, target, Target", file=sys.stderr)
        return
    
    print(f"使用标签列: {label_col}")
    
    # 分离特征和标签
    y_train = train_df[label_col]
    X_train_raw = train_df.drop(label_col, axis=1)
    
    y_val = val_df[label_col]
    X_val_raw = val_df.drop(label_col, axis=1)
    
    # 确保特征数据只包含数值列
    print("检查特征数据类型...")
    numeric_cols = X_train_raw.select_dtypes(include=[np.number]).columns
    non_numeric_cols = X_train_raw.select_dtypes(exclude=[np.number]).columns
    
    if len(non_numeric_cols) > 0:
        print(f"警告：发现非数值列，将被排除: {non_numeric_cols.tolist()}")
        X_train_raw = X_train_raw[numeric_cols]
        X_val_raw = X_val_raw[numeric_cols]
    
    features = X_train_raw.columns.tolist()
    print(f"使用 {len(features)} 个数值特征进行建模")
    
    # 标签编码
    le = LabelEncoder()
    y_train = le.fit_transform(y_train)
    y_val = le.transform(y_val)
    class_names = le.classes_
    eng_class_names = [CLASS_LABEL_MAP.get(name, name) for name in class_names]
    
    print(f"类别映射: {dict(zip(class_names, eng_class_names))}")

    # 双数据处理流水线
    # 流水线1: TabPFN (使用原始数据)
    print("为TabPFN准备数据 (原始)...")
    X_train_tabpfn = X_train_raw.values
    X_val_tabpfn = X_val_raw.values
    
    # 流水线2: 标准模型 (CLR + 标准化)
    print("为标准模型准备数据 (CLR + 标准化)...")
    try:
        X_train_clr = clr_transform(X_train_raw.copy())
        X_val_clr = clr_transform(X_val_raw.copy())
        
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train_clr)
        X_val_scaled = scaler.transform(X_val_clr)
        
    except Exception as e:
        print(f"CLR转换失败: {e}")
        print("使用标准化替代CLR转换...")
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train_raw)
        X_val_scaled = scaler.transform(X_val_raw)

    # 保存标准化模型和标签编码器
    scaler_path = os.path.join(model_dir, 'scaler.joblib')
    dump(scaler, scaler_path)
    print(f"标准化模型已保存: {scaler_path}")
    
    le_path = os.path.join(model_dir, 'label_encoder.joblib')
    dump(le, le_path)
    print(f"标签编码器已保存: {le_path}")
    
    # 4. 训练模型 (直接在提供的训练集上)
    print("\n训练个体模型...")
    optimized_models = {}
    
    # 随机森林
    print("训练随机森林...")
    rf_model = RandomForestClassifier(random_state=RANDOM_STATE, n_jobs=N_JOBS, class_weight='balanced', n_estimators=200, max_depth=10)
    rf_model.fit(X_train_scaled, y_train)
    optimized_models['Random Forest'] = rf_model
    
    # LightGBM
    print("训练LightGBM...")
    lgbm_model = lgb.LGBMClassifier(random_state=RANDOM_STATE, verbose=-1, force_row_wise=True, objective='binary')
    lgbm_model.fit(X_train_scaled, y_train)
    optimized_models['LightGBM'] = lgbm_model
    
    # 逻辑回归
    print("训练逻辑回归...")
    lr_model = LogisticRegression(random_state=RANDOM_STATE, max_iter=5000, n_jobs=N_JOBS, class_weight='balanced')
    lr_model.fit(X_train_scaled, y_train)
    optimized_models['Logistic Regression'] = lr_model

    # TabPFN
    print("\n训练TabPFN模型...")
    try:
        device = check_cuda_availability()
        tabpfn_model = TabPFNClassifier(device=device)
        tabpfn_model.fit(X_train_tabpfn, y_train)
        optimized_models['TabPFN'] = tabpfn_model
        print("TabPFN模型训练完成。")
    except Exception as e:
        print(f"TabPFN模型训练出错: {str(e)}", file=sys.stderr)
        traceback.print_exc()

    # 5. 交叉验证分析
    print("\n执行交叉验证分析...")
    X_data_dict = {
        'standard': X_train_scaled,
        'tabpfn': X_train_tabpfn
    }
    cv_results = perform_cross_validation_analysis(optimized_models, X_data_dict, y_train)
    
    # 6. 评估模型
    print("\n评估模型...")
    train_metrics, val_metrics = {}, {}
    y_train_probas, y_val_probas = {}, {}
    
    for name, model in optimized_models.items():
        # 选择正确的数据流
        is_tabpfn = (name == 'TabPFN')
        X_train_eval = X_train_tabpfn if is_tabpfn else X_train_scaled
        X_val_eval = X_val_tabpfn if is_tabpfn else X_val_scaled
        
        # 训练集性能
        y_train_pred = model.predict(X_train_eval)
        train_metrics[name] = {
            'accuracy': accuracy_score(y_train, y_train_pred),
            'f1': f1_score(y_train, y_train_pred, average='weighted')
        }
        if hasattr(model, 'predict_proba'):
            y_train_probas[name] = model.predict_proba(X_train_eval)[:, 1]
        
        # 验证集性能
        y_val_pred = model.predict(X_val_eval)
        val_metrics[name] = {
            'accuracy': accuracy_score(y_val, y_val_pred),
            'f1': f1_score(y_val, y_val_pred, average='weighted'),
            'report': classification_report(y_val, y_val_pred, target_names=eng_class_names, output_dict=True)
        }
        if hasattr(model, 'predict_proba'):
            y_val_probas[name] = model.predict_proba(X_val_eval)[:, 1]

        # 打印性能
        print(f"--- {name} ---")
        print(f"  训练集 - Accuracy: {train_metrics[name]['accuracy']:.4f}, F1-Score: {train_metrics[name]['f1']:.4f}")
        print(f"  验证集 - Accuracy: {val_metrics[name]['accuracy']:.4f}, F1-Score: {val_metrics[name]['f1']:.4f}")
        print(f"  交叉验证 - F1: {cv_results[name]['cv_f1_mean']:.4f}±{cv_results[name]['cv_f1_std']:.4f}")

    # 7. 生成可视化分析
    print("\n生成可视化分析...")
    
    # 准备数据字典用于可视化
    X_train_dict = {'standard': X_train_scaled, 'tabpfn': X_train_tabpfn}
    X_val_dict = {'standard': X_val_scaled, 'tabpfn': X_val_tabpfn}
    
    # 混淆矩阵 (训练集和验证集)
    plot_confusion_matrices(optimized_models, X_train_dict, X_val_dict, None, 
                           y_train, y_val, None, eng_class_names, 
                           visualization_dir, fish_type)
    
    # 交叉验证性能对比
    plot_cv_performance_comparison(cv_results, visualization_dir, fish_type)
    
    # 单个生物标志物分析 (训练集和验证集)
    single_biomarker_data = create_single_biomarker_comparison(
        X_train_scaled, X_val_scaled, None, 
        y_train, y_val, None, features, optimized_models
    )
    
    # 增强版ROC曲线 (训练集性能)
    if y_train_probas:
        optimal_thresholds, _, _ = plot_enhanced_roc_curves(
            y_train_probas, y_train, visualization_dir, 
            "Training", single_biomarker_data, fish_type
        )
    
    # 保存性能指标到文本文件
    save_metrics_to_text(val_metrics, visualization_dir, fish_type, train_metrics, 
                        optimal_thresholds, None, cv_results)

    # 8. 创建综合PDF报告
    print("\n创建PDF报告...")
    create_comprehensive_pdf_report(visualization_dir, fish_type, optimized_models, 
                                  cv_results, optimal_thresholds, train_metrics, 
                                  val_metrics, None)
    
    # 整理所有可视化为PDF集合
    save_all_visualizations_as_pdf(visualization_dir, fish_type)

    # 9. 保存模型
    print("\n保存模型...")
    for name, model in optimized_models.items():
        model_path = os.path.join(model_dir, f"{name.replace(' ', '_').lower()}_model.joblib")
        dump(model, model_path)
        print(f"模型已保存: {model_path}")
    
    print("\n模型训练与评估流程完成!")
    print(f"所有结果已保存到: {output_dir}")
    print(f"可视化文件位于: {visualization_dir}")
    print(f"模型文件位于: {model_dir}")

def get_scorers():
    """定义交叉验证中使用的评估指标"""
    return {
        'f1_macro': make_scorer(f1_score, average='macro')
    }

def save_metrics_to_text(metrics, output_dir, species_name='鲢鱼', train_metrics=None, 
                        optimal_thresholds=None, test_metrics=None, cv_results=None):
    """将模型性能指标和最佳阈值保存为文本文件"""
    print("保存性能指标到文本文件...")
    eng_species = SPECIES_MAP.get(species_name, species_name)
    output_path = os.path.join(output_dir, f"{eng_species}_model_metrics.txt")
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(f"======== {eng_species} Model Performance Evaluation ========\n")
        f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 训练集性能
        if train_metrics:
            f.write("== Training Set Performance ==\n")
            for name, m in train_metrics.items():
                f.write(f"  {name}:\n")
                f.write(f"    Accuracy: {m['accuracy']:.4f}\n")
                f.write(f"    F1-Score: {m['f1']:.4f}\n")
            f.write("\n")
        
        # 验证集性能
        f.write("== Validation Set Performance ==\n")
        for name, m in metrics.items():
            f.write(f"  {name}:\n")
            f.write(f"    Accuracy: {m['accuracy']:.4f}\n")
            f.write(f"    F1-Score: {m['f1']:.4f}\n")
        f.write("\n")
        
        # 测试集性能
        if test_metrics:
            f.write("== Test Set Performance ==\n")
            for name, m in test_metrics.items():
                f.write(f"  {name}:\n")
                f.write(f"    Accuracy: {m['accuracy']:.4f}\n")
                f.write(f"    F1-Score: {m['f1']:.4f}\n")
                if 'report' in m:
                    f.write(f"    Detailed Classification Report:\n")
                    for class_name, class_metrics in m['report'].items():
                        if isinstance(class_metrics, dict) and 'precision' in class_metrics:
                            f.write(f"      {class_name}:\n")
                            f.write(f"        Precision: {class_metrics['precision']:.4f}\n")
                            f.write(f"        Recall: {class_metrics['recall']:.4f}\n")
                            f.write(f"        F1-Score: {class_metrics['f1-score']:.4f}\n")
            f.write("\n")
        
        # 交叉验证结果
        if cv_results:
            f.write("== Cross-Validation Performance ==\n")
            for name, cv_m in cv_results.items():
                f.write(f"  {name}:\n")
                f.write(f"    CV Accuracy: {cv_m['cv_accuracy_mean']:.4f} ± {cv_m['cv_accuracy_std']:.4f}\n")
                f.write(f"    CV Precision: {cv_m['cv_precision_mean']:.4f} ± {cv_m['cv_precision_std']:.4f}\n")
                f.write(f"    CV Recall: {cv_m['cv_recall_mean']:.4f} ± {cv_m['cv_recall_std']:.4f}\n")
                f.write(f"    CV F1-Score: {cv_m['cv_f1_mean']:.4f} ± {cv_m['cv_f1_std']:.4f}\n")
            f.write("\n")
        
        # 最佳阈值
        if optimal_thresholds:
            f.write("== Optimal Prediction Thresholds ==\n")
            for name, t in optimal_thresholds.items():
                f.write(f"  {name}: {t:.4f}\n")
            f.write("\n")
        
        # 模型配置信息
        f.write("== Model Configuration ==\n")
        f.write(f"  Cross-Validation Splits: {CV_SPLITS}\n")
        f.write(f"  Cross-Validation Repeats: {CV_REPEATS}\n")
        f.write(f"  Random State: {RANDOM_STATE}\n")
        f.write(f"  Number of Jobs: {N_JOBS}\n")
    
    print(f"性能指标已保存到 {output_path}")
    return output_path

def plot_roc_curves(y_probas, y_true, output_dir, file_suffix=''):
    """绘制ROC曲线并寻找最佳阈值"""
    print(f"绘制ROC曲线 for {file_suffix}...")
    eng_species = SPECIES_MAP.get(file_suffix.split('_')[0], file_suffix.split('_')[0])
    plt.figure(figsize=(10, 8))
    optimal_thresholds = {}
    for name, y_prob in y_probas.items():
        fpr, tpr, thresholds = roc_curve(y_true, y_prob)
        roc_auc = auc(fpr, tpr)
        J = tpr - fpr
        ix = np.argmax(J)
        best_thresh = thresholds[ix]
        optimal_thresholds[name] = best_thresh
        plt.plot(fpr, tpr, lw=2, label=f'{name} (AUC = {roc_auc:.3f}, Thresh = {best_thresh:.3f})')
    
    plt.plot([0, 1], [0, 1], 'k--', lw=2)
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title(f'ROC Curves for {eng_species} Models ({file_suffix.split("_")[-1]})')
    plt.legend(loc="lower right")
    plt.grid(True)
    
    output_path = os.path.join(output_dir, f"{eng_species}_{file_suffix}_roc_curves.png")
    plt.savefig(output_path, dpi=300)
    plt.close()
    print(f"ROC曲线图已保存到 {output_path}")
    return optimal_thresholds

def plot_confusion_matrices(models, X_train, X_val, X_test, y_train, y_val, y_test, 
                           class_names, output_dir, species_name):
    """为每个模型绘制混淆矩阵（训练、验证集）- 组合标志物性能"""
    print("绘制混淆矩阵...")
    eng_species = SPECIES_MAP.get(species_name, species_name)
    
    # 为每个模型创建混淆矩阵
    for model_name, model in models.items():
        fig, axes = plt.subplots(1, 2, figsize=(16, 6))
        fig.suptitle(f'{model_name} - Confusion Matrices ({eng_species})', 
                    fontsize=16, fontweight='bold')
        
        # 选择正确的数据
        is_tabpfn = (model_name == 'TabPFN')
        X_train_eval = X_train['tabpfn'] if is_tabpfn else X_train['standard']
        X_val_eval = X_val['tabpfn'] if is_tabpfn else X_val['standard']
        
        # 预测
        y_train_pred = model.predict(X_train_eval)
        y_val_pred = model.predict(X_val_eval)
        
        datasets = [
            ('Training Set', y_train, y_train_pred, axes[0]),
            ('Validation Set', y_val, y_val_pred, axes[1])
        ]
        
        for title, y_true, y_pred, ax in datasets:
            cm = confusion_matrix(y_true, y_pred)
            acc = accuracy_score(y_true, y_pred)
            
            # 绘制热力图 - 修复数值显示问题
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                       xticklabels=class_names, yticklabels=class_names,
                       ax=ax, 
                       annot_kws={"size": 16, "weight": "bold", "color": "black"},
                       cbar_kws={"shrink": 0.8},
                       linewidths=2, linecolor='white')
            
            ax.set_title(f'{title}\nAccuracy: {acc:.3f}', fontsize=14, fontweight='bold')
            ax.set_ylabel('True Label', fontsize=12, fontweight='bold')
            ax.set_xlabel('Predicted Label', fontsize=12, fontweight='bold')
            
            # 添加百分比标注 - 确保数值可见
            for i in range(len(class_names)):
                for j in range(len(class_names)):
                    if cm[i, j] > 0:
                        percentage = cm[i, j] / cm[i, :].sum() * 100
                        ax.text(j + 0.5, i + 0.75, f'({percentage:.1f}%)', 
                               ha='center', va='center', fontsize=12, 
                               color='red', fontweight='bold')
        
        plt.tight_layout()
        
        # 保存PNG和PDF
        model_name_clean = model_name.replace(' ', '_').lower()
        png_path = os.path.join(output_dir, f"{eng_species}_{model_name_clean}_confusion_matrices.png")
        pdf_path = os.path.join(output_dir, f"{eng_species}_{model_name_clean}_confusion_matrices.pdf")
        
        plt.savefig(png_path, dpi=300, bbox_inches='tight')
        plt.savefig(pdf_path, bbox_inches='tight', format='pdf')
        plt.close()
        
        print(f"混淆矩阵已保存: {png_path}")
        print(f"PDF版本已保存: {pdf_path}")

def plot_single_confusion_matrix(y_true, y_pred, class_names, title, output_dir, 
                                species_name, model_name):
    """绘制单个混淆矩阵"""
    eng_species = SPECIES_MAP.get(species_name, species_name)
    
    plt.figure(figsize=(8, 6))
    cm = confusion_matrix(y_true, y_pred)
    acc = accuracy_score(y_true, y_pred)
    
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
               xticklabels=class_names, yticklabels=class_names,
               annot_kws={"size": 16, "weight": "bold"},
               cbar_kws={"shrink": 0.8},
               linewidths=2, linecolor='white')
    
    plt.title(f'{title} - {model_name}\n({eng_species}) Accuracy: {acc:.3f}', 
             fontsize=16, fontweight='bold')
    plt.ylabel('True Label', fontsize=14)
    plt.xlabel('Predicted Label', fontsize=14)
    
    # 添加百分比
    for i in range(len(class_names)):
        for j in range(len(class_names)):
            if cm[i, j] > 0:
                percentage = cm[i, j] / cm[i, :].sum() * 100
                plt.text(j + 0.5, i + 0.7, f'({percentage:.1f}%)', 
                        ha='center', va='center', fontsize=12, color='red')
    
    plt.tight_layout()
    
    # 保存文件
    filename = f"{eng_species}_{model_name.replace(' ', '_').lower()}_{title.replace(' ', '_').lower()}_cm"
    png_path = os.path.join(output_dir, f"{filename}.png")
    pdf_path = os.path.join(output_dir, f"{filename}.pdf")
    
    plt.savefig(png_path, dpi=300, bbox_inches='tight')
    plt.savefig(pdf_path, bbox_inches='tight', format='pdf')
    plt.close()
    
    return png_path, pdf_path

def plot_cv_performance_comparison(cv_results, output_dir, species_name):
    """绘制交叉验证性能对比条形图（顶刊样式）"""
    print("绘制交叉验证性能对比图...")
    eng_species = SPECIES_MAP.get(species_name, species_name)
    
    # 准备数据
    models = list(cv_results.keys())
    metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score']
    
    # 提取均值和标准差
    means = {metric: [] for metric in metrics}
    stds = {metric: [] for metric in metrics}
    
    for model in models:
        means['Accuracy'].append(cv_results[model]['cv_accuracy_mean'])
        stds['Accuracy'].append(cv_results[model]['cv_accuracy_std'])
        means['Precision'].append(cv_results[model]['cv_precision_mean'])
        stds['Precision'].append(cv_results[model]['cv_precision_std'])
        means['Recall'].append(cv_results[model]['cv_recall_mean'])
        stds['Recall'].append(cv_results[model]['cv_recall_std'])
        means['F1-Score'].append(cv_results[model]['cv_f1_mean'])
        stds['F1-Score'].append(cv_results[model]['cv_f1_std'])
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'Cross-Validation Performance Comparison ({eng_species})', 
                fontsize=18, fontweight='bold')
    
    # 顶刊样式配色
    colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D']
    
    axes_flat = axes.flatten()
    
    for i, metric in enumerate(metrics):
        ax = axes_flat[i]
        
        # 绘制条形图
        bars = ax.bar(models, means[metric], yerr=stds[metric], 
                     capsize=5, color=colors, alpha=0.8, 
                     edgecolor='black', linewidth=1.2)
        
        # 添加数值标签
        for j, (bar, mean, std) in enumerate(zip(bars, means[metric], stds[metric])):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + std + 0.01,
                   f'{mean:.3f}±{std:.3f}', ha='center', va='bottom', 
                   fontsize=10, fontweight='bold')
        
        ax.set_title(f'{metric}', fontsize=14, fontweight='bold')
        ax.set_ylabel(f'{metric} Score', fontsize=12)
        ax.set_ylim(0, 1.1)
        ax.grid(True, alpha=0.3, axis='y')
        ax.set_axisbelow(True)
        
        # 旋转x轴标签
        ax.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    
    # 保存文件
    png_path = os.path.join(output_dir, f"{eng_species}_cv_performance_comparison.png")
    pdf_path = os.path.join(output_dir, f"{eng_species}_cv_performance_comparison.pdf")
    
    plt.savefig(png_path, dpi=300, bbox_inches='tight')
    plt.savefig(pdf_path, bbox_inches='tight', format='pdf')
    plt.close()
    
    print(f"交叉验证性能图已保存: {png_path}")
    print(f"PDF版本已保存: {pdf_path}")
    
    return png_path, pdf_path

def perform_cross_validation_analysis(models, X_data, y_train, cv_splits=10, n_repeats=5):
    """执行交叉验证分析"""
    print("执行交叉验证分析...")
    
    cv_results = {}
    cv_splitter = RepeatedStratifiedKFold(n_splits=cv_splits, n_repeats=n_repeats, 
                                         random_state=RANDOM_STATE)
    
    scoring = ['accuracy', 'precision_weighted', 'recall_weighted', 'f1_weighted']
    
    for model_name, model in models.items():
        print(f"对{model_name}执行交叉验证...")
        
        # 选择正确的数据
        is_tabpfn = (model_name == 'TabPFN')
        X_cv = X_data['tabpfn'] if is_tabpfn else X_data['standard']
        
        try:
            cv_scores = cross_validate(
                model, X_cv, y_train, cv=cv_splitter,
                scoring=scoring, n_jobs=N_JOBS, return_train_score=True
            )
            
            cv_results[model_name] = {
                'cv_accuracy_mean': np.mean(cv_scores['test_accuracy']),
                'cv_accuracy_std': np.std(cv_scores['test_accuracy']),
                'cv_precision_mean': np.mean(cv_scores['test_precision_weighted']),
                'cv_precision_std': np.std(cv_scores['test_precision_weighted']),
                'cv_recall_mean': np.mean(cv_scores['test_recall_weighted']),
                'cv_recall_std': np.std(cv_scores['test_recall_weighted']),
                'cv_f1_mean': np.mean(cv_scores['test_f1_weighted']),
                'cv_f1_std': np.std(cv_scores['test_f1_weighted'])
            }
            
        except Exception as e:
            print(f"模型{model_name}交叉验证失败: {e}")
            cv_results[model_name] = {
                'cv_accuracy_mean': 0, 'cv_accuracy_std': 0,
                'cv_precision_mean': 0, 'cv_precision_std': 0,
                'cv_recall_mean': 0, 'cv_recall_std': 0,
                'cv_f1_mean': 0, 'cv_f1_std': 0
            }
    
    return cv_results

def plot_enhanced_roc_curves(y_probas, y_true, output_dir, file_suffix='', 
                           single_biomarker_data=None, species_name='鲢鱼'):
    """增强版ROC曲线绘制，支持单个vs组合标志物对比 - 训练集性能"""
    print(f"绘制增强版ROC曲线 for {file_suffix}...")
    eng_species = SPECIES_MAP.get(species_name, species_name)
    
    plt.figure(figsize=(12, 10))
    
    optimal_thresholds = {}
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
    
    # 绘制组合标志物ROC曲线
    for i, (name, y_prob) in enumerate(y_probas.items()):
        fpr, tpr, thresholds = roc_curve(y_true, y_prob)
        roc_auc = auc(fpr, tpr)
        
        # 计算最佳阈值（Youden指数）
        J = tpr - fpr
        ix = np.argmax(J)
        best_thresh = thresholds[ix]
        optimal_thresholds[name] = best_thresh
        
        # 计算95%置信区间（bootstrap方法）
        n_bootstraps = 1000
        rng = np.random.RandomState(RANDOM_STATE)
        bootstrapped_scores = []
        
        for _ in range(n_bootstraps):
            indices = rng.randint(0, len(y_prob), len(y_prob))
            if len(np.unique(y_true[indices])) < 2:
                continue
            try:
                score = auc(*roc_curve(y_true[indices], y_prob[indices])[:2])
                bootstrapped_scores.append(score)
            except ValueError:
                continue
        
        if bootstrapped_scores:
            ci_lower = np.percentile(bootstrapped_scores, 2.5)
            ci_upper = np.percentile(bootstrapped_scores, 97.5)
            ci_text = f" (95% CI: {ci_lower:.3f}-{ci_upper:.3f})"
        else:
            ci_text = ""
        
        plt.plot(fpr, tpr, color=colors[i % len(colors)], lw=3,
                label=f'{name} (AUC = {roc_auc:.3f}{ci_text})')
        
        # 标记最佳阈值点
        plt.plot(fpr[ix], tpr[ix], 'o', color=colors[i % len(colors)], 
                markersize=8, markerfacecolor='white', markeredgewidth=2)
    
    # 如果提供了单个生物标志物数据，添加对比
    if single_biomarker_data and 'training' in single_biomarker_data:
        biomarker_data = single_biomarker_data['training']
        for biomarker_name, biomarker_prob in biomarker_data.items():
            try:
                fpr_single, tpr_single, _ = roc_curve(y_true, biomarker_prob)
                auc_single = auc(fpr_single, tpr_single)
                plt.plot(fpr_single, tpr_single, '--', alpha=0.7, lw=2,
                        label=f'{biomarker_name} (Single, AUC = {auc_single:.3f})')
            except ValueError:
                continue
    
    # 绘制对角线
    plt.plot([0, 1], [0, 1], 'k--', lw=2, alpha=0.5, label='Random Classifier')
    
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate', fontsize=14, fontweight='bold')
    plt.ylabel('True Positive Rate', fontsize=14, fontweight='bold')
    plt.title(f'ROC Curves Comparison ({eng_species}) - Training Set Performance', 
             fontsize=16, fontweight='bold')
    plt.legend(loc="lower right", fontsize=11)
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息文本框
    textstr = f'Best Thresholds:\n'
    for name, thresh in optimal_thresholds.items():
        textstr += f'{name}: {thresh:.3f}\n'
    
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
    plt.text(0.02, 0.98, textstr, transform=plt.gca().transAxes, fontsize=10,
            verticalalignment='top', bbox=props)
    
    plt.tight_layout()
    
    # 保存文件
    filename_base = f"{eng_species}_training_enhanced_roc_curves"
    png_path = os.path.join(output_dir, f"{filename_base}.png")
    pdf_path = os.path.join(output_dir, f"{filename_base}.pdf")
    
    plt.savefig(png_path, dpi=300, bbox_inches='tight')
    plt.savefig(pdf_path, bbox_inches='tight', format='pdf')
    plt.close()
    
    print(f"增强版ROC曲线已保存: {png_path}")
    print(f"PDF版本已保存: {pdf_path}")
    
    return optimal_thresholds, png_path, pdf_path

def create_single_biomarker_comparison(X_train, X_val, X_test, y_train, y_val, y_test, 
                                     features, models, top_n=5):
    """创建单个生物标志物性能数据用于ROC对比 - 基于训练集"""
    print("分析单个生物标志物性能...")
    
    # 返回训练集和验证集的单个生物标志物结果
    single_biomarker_results = {
        'training': {},
        'validation': {}
    }
    
    # 使用随机森林的特征重要性选择top特征
    rf_model = models.get('Random Forest')
    if rf_model and hasattr(rf_model, 'feature_importances_'):
        importances = rf_model.feature_importances_
        top_indices = np.argsort(importances)[-top_n:]
        
        for idx in top_indices:
            feature_name = features[idx]
            
            # 使用单个特征训练简单的逻辑回归
            single_feature_train = X_train[:, idx].reshape(-1, 1)
            single_feature_val = X_val[:, idx].reshape(-1, 1)
            
            lr_single = LogisticRegression(random_state=RANDOM_STATE)
            lr_single.fit(single_feature_train, y_train)
            
            # 预测概率 - 分别为训练集和验证集
            y_prob_train = lr_single.predict_proba(single_feature_train)[:, 1]
            y_prob_val = lr_single.predict_proba(single_feature_val)[:, 1]
            
            single_biomarker_results['training'][feature_name] = y_prob_train
            single_biomarker_results['validation'][feature_name] = y_prob_val
    
    return single_biomarker_results

def create_comprehensive_pdf_report(output_dir, species_name, models, cv_results, 
                                  optimal_thresholds, train_metrics, val_metrics, test_metrics):
    """创建综合PDF报告"""
    print("创建综合PDF报告...")
    eng_species = SPECIES_MAP.get(species_name, species_name)
    
    pdf_path = os.path.join(output_dir, f"{eng_species}_comprehensive_report.pdf")
    
    with PdfPages(pdf_path) as pdf:
        # 第1页：模型性能总结表
        fig, ax = plt.subplots(figsize=(12, 8))
        ax.axis('tight')
        ax.axis('off')
        
        # 创建性能总结表
        table_data = []
        headers = ['Model', 'Train Acc', 'Val Acc', 'Test Acc', 'Train F1', 'Val F1', 'Test F1', 'CV F1±Std']
        
        for model_name in models.keys():
            row = [
                model_name,
                f"{train_metrics[model_name]['accuracy']:.3f}",
                f"{val_metrics[model_name]['accuracy']:.3f}",
                f"{test_metrics[model_name]['accuracy']:.3f}",
                f"{train_metrics[model_name]['f1']:.3f}",
                f"{val_metrics[model_name]['f1']:.3f}",
                f"{test_metrics[model_name]['f1']:.3f}",
                f"{cv_results[model_name]['cv_f1_mean']:.3f}±{cv_results[model_name]['cv_f1_std']:.3f}"
            ]
            table_data.append(row)
        
        table = ax.table(cellText=table_data, colLabels=headers, 
                        cellLoc='center', loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 1.5)
        
        # 设置表格样式
        for i in range(len(headers)):
            table[(0, i)].set_facecolor('#4CAF50')
            table[(0, i)].set_text_props(weight='bold', color='white')
        
        plt.title(f'{eng_species} Model Performance Summary', 
                 fontsize=16, fontweight='bold', pad=20)
        pdf.savefig(fig, bbox_inches='tight')
        plt.close()
        
        # 第2页：最佳阈值信息
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.axis('tight')
        ax.axis('off')
        
        threshold_data = []
        for model_name, threshold in optimal_thresholds.items():
            threshold_data.append([model_name, f"{threshold:.4f}"])
        
        threshold_table = ax.table(cellText=threshold_data, 
                                 colLabels=['Model', 'Optimal Threshold'],
                                 cellLoc='center', loc='center')
        threshold_table.auto_set_font_size(False)
        threshold_table.set_fontsize(12)
        threshold_table.scale(1.5, 2)
        
        plt.title(f'{eng_species} Optimal Prediction Thresholds', 
                 fontsize=16, fontweight='bold', pad=20)
        pdf.savefig(fig, bbox_inches='tight')
        plt.close()
        
        # 添加元数据
        d = pdf.infodict()
        d['Title'] = f'{eng_species} Machine Learning Model Report'
        d['Author'] = 'Automated ML Pipeline'
        d['Subject'] = 'Model Performance Analysis'
        d['Keywords'] = f'{species_name}, Machine Learning, Classification'
        d['CreationDate'] = datetime.now()
    
    print(f"综合PDF报告已保存: {pdf_path}")
    return pdf_path

def save_all_visualizations_as_pdf(output_dir, species_name):
    """将所有PNG可视化转换为PDF集合"""
    print("整理所有可视化为PDF集合...")
    eng_species = SPECIES_MAP.get(species_name, species_name)
    
    # 查找所有PNG文件
    png_files = []
    for file in os.listdir(output_dir):
        if file.endswith('.png') and eng_species in file:
            png_files.append(os.path.join(output_dir, file))
    
    if not png_files:
        print("未找到PNG文件")
        return
    
    # 创建PDF集合
    collection_pdf_path = os.path.join(output_dir, f"{eng_species}_all_visualizations.pdf")
    
    with PdfPages(collection_pdf_path) as pdf:
        for png_file in sorted(png_files):
            fig = plt.figure(figsize=(12, 8))
            img = plt.imread(png_file)
            plt.imshow(img)
            plt.axis('off')
            plt.title(os.path.basename(png_file).replace('.png', '').replace('_', ' ').title(), 
                     fontsize=14, fontweight='bold')
            pdf.savefig(fig, bbox_inches='tight')
            plt.close()
    
    print(f"可视化PDF集合已保存: {collection_pdf_path}")
    return collection_pdf_path

if __name__ == "__main__":
    
    # --- 用户配置区 ---
    # 请在此处提供预先划分好的数据集路径
    BASE_DATA_DIR = "C:/Users/<USER>/Desktop/Boruta_Pipeline_V2_Output/2_Elite_Features_Data"
    OUTPUT_DIR = "C:/Users/<USER>/Desktop/Python_Pipeline_Output"
    FISH_TYPE = "鲢鱼" # 指定当前处理的鱼种
    
    TRAIN_DATA_PATH = os.path.join(BASE_DATA_DIR, "elite_features_TRAIN_raw.csv")
    VAL_DATA_PATH = os.path.join(BASE_DATA_DIR, "elite_features_VALIDATION_raw.csv")
    TEST_DATA_PATH = os.path.join(BASE_DATA_DIR, "elite_features_TEST_raw.csv")
    # ------------------

    try:
        train_and_evaluate_models(
            train_path=TRAIN_DATA_PATH,
            val_path=VAL_DATA_PATH,
            test_path=TEST_DATA_PATH,
            output_dir=OUTPUT_DIR,
            fish_type=FISH_TYPE
        )
    except Exception as e:
        print(f"\nFATAL ERROR: 程序执行失败。", file=sys.stderr)
        traceback.print_exc()
        sys.exit(1)





























