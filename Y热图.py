# -*- coding: utf-8 -*-
"""
visualize_differential_heatmap.py

顶级期刊标准的差异菌群热图可视化脚本
功能：
1.  读取多个分类学水平的特征丰度表
2.  筛选两组间平均丰度差异最显著的前N个特征
3.  对差异特征进行Z-score标准化
4.  生成符合顶级期刊标准的高质量热图（PDF可编辑格式）
5.  支持小尺寸输出，适合期刊发表

特点：
- 符合Nature/Science等顶级期刊图表规范
- PDF矢量格式，完全可编辑
- 优化的字体和色彩方案
- 小尺寸高质量输出

依赖库:
pip install pandas numpy seaborn matplotlib scipy
"""

import os
import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from datetime import datetime
import logging
from scipy.cluster import hierarchy as sch
import matplotlib.patches as mpatches
from matplotlib import rcParams

# --- 期刊标准配置 ---
# 设置matplotlib参数以符合顶级期刊标准
rcParams.update({
    'font.family': 'Arial',  # 期刊推荐字体
    'font.size': 8,          # 基础字体大小
    'axes.titlesize': 9,     # 标题字体大小
    'axes.labelsize': 8,     # 轴标签字体大小
    'xtick.labelsize': 7,    # X轴刻度标签字体大小
    'ytick.labelsize': 7,    # Y轴刻度标签字体大小
    'legend.fontsize': 7,    # 图例字体大小
    'figure.titlesize': 10,  # 图形标题字体大小
    'pdf.fonttype': 42,      # 确保PDF中字体可编辑
    'ps.fonttype': 42,       # PostScript字体类型
    'svg.fonttype': 'none',  # SVG字体类型
    'axes.linewidth': 0.5,   # 坐标轴线宽
    'xtick.major.width': 0.5,# X轴主刻度线宽
    'ytick.major.width': 0.5,# Y轴主刻度线宽
    'lines.linewidth': 0.8,  # 线条宽度
})

# --- 日志记录设置 ---
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S')

class DifferentialHeatmapVisualizer:
    """
    符合顶级期刊标准的差异特征热图可视化器

    特点：
    - 小尺寸高质量输出
    - PDF矢量格式，完全可编辑
    - 符合Nature/Science等期刊规范
    """
    def __init__(self, input_files_dict, top_n=10, output_base_dir=None,
                 figure_format='journal', dpi=300):
        """
        初始化可视化器

        参数:
        - input_files_dict (dict): 分类学水平到文件路径的映射
        - top_n (int): 筛选的差异特征数量，默认10
        - output_base_dir (str): 输出目录，默认为桌面
        - figure_format (str): 图形格式 ('journal', 'presentation', 'custom')
        - dpi (int): 图像分辨率，默认300
        """
        self.input_files = input_files_dict
        self.top_n = top_n
        self.dpi = dpi

        if output_base_dir is None:
            output_base_dir = os.path.join(os.path.expanduser('~'), 'Desktop')

        timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
        self.output_dir = os.path.join(output_base_dir, f"Journal_Heatmap_Analysis_{timestamp}")

        self.sample_id_col = "SampleID"
        self.group_col = "Group"

        # 期刊标准图形配置
        self.figure_configs = {
            'journal': {
                'single_column_width': 3.5,  # 英寸，单栏宽度
                'double_column_width': 7.0,  # 英寸，双栏宽度
                'max_height': 9.0,           # 英寸，最大高度
                'aspect_ratio': 1.2,         # 高宽比
            },
            'presentation': {
                'single_column_width': 6.0,
                'double_column_width': 10.0,
                'max_height': 8.0,
                'aspect_ratio': 0.8,
            }
        }

        self.current_config = self.figure_configs.get(figure_format, self.figure_configs['journal'])

        # 期刊标准色彩方案
        self.color_schemes = {
            'nature': {'CY': '#E64B35', 'WY': '#4DBBD5'},  # Nature风格
            'science': {'CY': '#DC143C', 'WY': '#4682B4'},  # Science风格
            'cell': {'CY': '#FF6B6B', 'WY': '#4ECDC4'},     # Cell风格
        }
        self.group_colors = self.color_schemes['nature']

        # 图例位置配置
        self.legend_positions = {
            'right': {'bbox_to_anchor': (1.05, 1.0), 'loc': 'upper left', 'ncol': 1},
            'bottom': {'bbox_to_anchor': (0.5, -0.15), 'loc': 'upper center', 'ncol': 2},
            'top': {'bbox_to_anchor': (0.5, 1.15), 'loc': 'lower center', 'ncol': 2},
            'left': {'bbox_to_anchor': (-0.05, 1.0), 'loc': 'upper right', 'ncol': 1},
        }

        os.makedirs(self.output_dir, exist_ok=True)
        logging.info(f"期刊标准热图输出目录: {self.output_dir}")

    def _get_short_name(self, feature_name):
        """从完整的分类学字符串中提取简称"""
        if isinstance(feature_name, str) and ';' in feature_name:
            return feature_name.split(';')[-1]
        return feature_name

    def _get_optimal_legend_position(self, fig_width, fig_height, n_samples, n_features):
        """
        根据图形尺寸和数据量智能选择最佳图例位置

        参数:
        - fig_width: 图形宽度
        - fig_height: 图形高度
        - n_samples: 样本数量
        - n_features: 特征数量

        返回:
        - legend_config: 图例配置字典
        """
        # 期刊标准图例位置选择逻辑
        if fig_width <= 4.0:  # 小图（单栏）
            if n_features <= 5:  # 特征少，使用底部
                return self.legend_positions['bottom']
            else:  # 特征多，使用右侧但调整位置
                return {'bbox_to_anchor': (1.02, 0.8), 'loc': 'upper left', 'ncol': 1}
        else:  # 大图（双栏）
            if n_samples <= 10:  # 样本少，使用顶部
                return self.legend_positions['top']
            else:  # 样本多，使用右侧
                return self.legend_positions['right']

    def _create_and_save_heatmap(self, data_df, level_name):
        """
        创建符合期刊标准的高质量热图

        参数:
        - data_df (pd.DataFrame): 包含样本信息和Z-score标准化数据的DataFrame
        - level_name (str): 当前的分类学水平名称
        """
        logging.info(f"创建期刊标准热图: {level_name} 水平...")

        # 准备绘图数据
        sample_info = data_df[[self.sample_id_col, self.group_col]].set_index(self.sample_id_col)
        heatmap_data = data_df.drop(columns=[self.group_col]).set_index(self.sample_id_col)

        # 根据分组排序，确保热图中分组清晰
        sorted_samples = sample_info.sort_values(by=self.group_col).index
        heatmap_data = heatmap_data.loc[sorted_samples]

        # 创建分组颜色条（使用期刊标准颜色）
        group_colors = sample_info.loc[sorted_samples, self.group_col].map(self.group_colors)

        # 计算最优图形尺寸
        n_samples = len(heatmap_data.index)
        n_features = len(heatmap_data.columns)

        # 根据样本数和特征数动态调整尺寸
        if n_samples <= 20:
            fig_width = self.current_config['single_column_width']
        else:
            fig_width = min(self.current_config['double_column_width'],
                          self.current_config['single_column_width'] + n_samples * 0.08)

        fig_height = min(self.current_config['max_height'],
                        max(2.5, n_features * 0.25 + 1.5))
        
        # 创建期刊标准热图
        try:
            # 计算特征聚类
            if n_features > 1:
                row_linkage = sch.linkage(heatmap_data.T, method='average', metric='euclidean')
            else:
                row_linkage = None

            # 创建热图，使用计算出的最优尺寸
            g = sns.clustermap(
                heatmap_data.T,  # 转置：特征在Y轴，样本在X轴
                figsize=(fig_width, fig_height),
                cmap='RdBu_r',   # 期刊推荐的发散色图
                center=0,        # Z-score中心为0
                col_colors=group_colors,  # 分组颜色条
                col_cluster=False,        # 保持样本排序
                row_linkage=row_linkage,  # 特征聚类
                linewidths=0.1,          # 细线条
                cbar_kws={
                    'label': 'Z-score',
                    'shrink': 0.8,
                    'aspect': 20,
                    'pad': 0.02
                },
                dendrogram_ratio=0.15,   # 树状图比例
                colors_ratio=0.03,       # 颜色条比例
            )

            # 优化轴标签和标题
            g.ax_heatmap.set_xlabel("Samples", fontsize=8, fontweight='bold')
            g.ax_heatmap.set_ylabel("Differential Features", fontsize=8, fontweight='bold')

            # 优化刻度标签
            plt.setp(g.ax_heatmap.get_xticklabels(), rotation=90, fontsize=6)
            plt.setp(g.ax_heatmap.get_yticklabels(), rotation=0, fontsize=7)

            # 期刊标准标题
            level_display = level_name.capitalize()
            g.figure.suptitle(f'Top {self.top_n} Differential Features ({level_display})',
                            fontsize=9, fontweight='bold', y=0.98)

            # 创建组别图例 - 智能位置选择
            legend_elements = [mpatches.Patch(color=color, label=group)
                             for group, color in self.group_colors.items()]

            # 使用智能图例位置选择
            legend_config = self._get_optimal_legend_position(fig_width, fig_height, n_samples, n_features)

            # 创建图例
            legend = g.ax_heatmap.legend(
                handles=legend_elements,
                title='Group',
                fontsize=7,
                title_fontsize=7,
                frameon=True,           # 添加边框以提高可读性
                fancybox=False,         # 简洁的方形边框
                shadow=False,           # 无阴影
                edgecolor='black',      # 黑色边框
                facecolor='white',      # 白色背景
                framealpha=0.9,         # 半透明背景
                borderpad=0.5,          # 内边距
                columnspacing=1.0,      # 列间距
                handlelength=1.5,       # 图例标记长度
                handletextpad=0.5,      # 标记与文本间距
                **legend_config
            )

            # 优化图例标题样式
            legend.get_title().set_fontweight('bold')
            legend.get_title().set_fontsize(7)

            # 保存期刊标准格式文件
            base_output_path = os.path.join(self.output_dir, f'journal_heatmap_{level_name}')

            # 保存高质量PDF（矢量格式，完全可编辑）
            pdf_path = f"{base_output_path}.pdf"
            g.figure.savefig(pdf_path,
                           dpi=self.dpi,
                           bbox_inches='tight',
                           pad_inches=0.1,
                           format='pdf',
                           facecolor='white',
                           edgecolor='none',
                           transparent=False)
            logging.info(f"期刊标准PDF已保存: {pdf_path}")

            # 保存高分辨率PNG（用于预览）
            png_path = f"{base_output_path}.png"
            g.figure.savefig(png_path,
                           dpi=self.dpi,
                           bbox_inches='tight',
                           pad_inches=0.1,
                           format='png',
                           facecolor='white',
                           edgecolor='none')
            logging.info(f"高分辨率PNG已保存: {png_path}")

            # 保存SVG格式（备选矢量格式）
            svg_path = f"{base_output_path}.svg"
            g.figure.savefig(svg_path,
                           bbox_inches='tight',
                           pad_inches=0.1,
                           format='svg',
                           facecolor='white',
                           edgecolor='none')
            logging.info(f"SVG矢量图已保存: {svg_path}")

            plt.close('all')

        except Exception as e:
            logging.error(f"创建 {level_name} 热图时出错: {e}")
            plt.close('all')

    def _create_compact_heatmap(self, data_df, level_name):
        """
        创建紧凑型热图，适合小尺寸输出和多面板图形

        参数:
        - data_df (pd.DataFrame): 包含样本信息和Z-score标准化数据的DataFrame
        - level_name (str): 当前的分类学水平名称
        """
        logging.info(f"创建紧凑型热图: {level_name} 水平...")

        # 准备数据
        sample_info = data_df[[self.sample_id_col, self.group_col]].set_index(self.sample_id_col)
        heatmap_data = data_df.drop(columns=[self.group_col]).set_index(self.sample_id_col)
        sorted_samples = sample_info.sort_values(by=self.group_col).index
        heatmap_data = heatmap_data.loc[sorted_samples]

        # 创建紧凑型图形
        fig, ax = plt.subplots(figsize=(2.5, 2.0))  # 小尺寸

        # 简化的热图
        im = ax.imshow(heatmap_data.T,
                      cmap='RdBu_r',
                      aspect='auto',
                      vmin=-2, vmax=2)  # 固定范围

        # 简化的标签
        ax.set_xticks([])  # 移除X轴刻度
        ax.set_yticks(range(len(heatmap_data.columns)))
        ax.set_yticklabels(heatmap_data.columns, fontsize=6)

        # 添加分组指示
        for group in sample_info.loc[sorted_samples, self.group_col].unique():
            group_samples = sample_info.loc[sorted_samples, self.group_col] == group
            start_idx = group_samples.idxmax()
            start_pos = sorted_samples.get_loc(start_idx)
            end_pos = start_pos + group_samples.sum() - 1
            mid_pos = (start_pos + end_pos) / 2

            # 在底部添加组别标签
            ax.text(mid_pos, -0.5, group,
                   ha='center', va='top', fontsize=7, fontweight='bold',
                   color=self.group_colors[group])

        # 简化的颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.6, aspect=15, pad=0.02)
        cbar.set_label('Z-score', fontsize=6)
        cbar.ax.tick_params(labelsize=5)

        # 标题
        ax.set_title(f'{level_name.capitalize()}', fontsize=8, fontweight='bold', pad=10)

        # 保存紧凑版本
        compact_path = os.path.join(self.output_dir, f'compact_heatmap_{level_name}.pdf')
        fig.savefig(compact_path,
                   dpi=self.dpi,
                   bbox_inches='tight',
                   pad_inches=0.05,
                   format='pdf')
        logging.info(f"紧凑型热图已保存: {compact_path}")

        plt.close(fig)

    def process_level(self, level_name, file_path):
        """
        处理单个分类学水平的文件。
        """
        logging.info(f"--- 开始处理: {level_name} 水平 ---")
        if not os.path.exists(file_path):
            logging.error(f"文件未找到，跳过: {file_path}")
            return
        
        try:
            data_df = pd.read_csv(file_path, sep='\t', header=0)
            logging.info(f"成功加载文件: {os.path.basename(file_path)}")
            
            if self.sample_id_col not in data_df.columns or self.group_col not in data_df.columns:
                logging.error(f"文件 {file_path} 中缺少关键列。")
                return

            feature_cols = data_df.columns.drop([self.sample_id_col, self.group_col])
            
            # 1. 识别差异特征
            group_means = data_df.groupby(self.group_col)[feature_cols].mean()
            if 'CY' not in group_means.index or 'WY' not in group_means.index:
                logging.warning(f"在 {level_name} 水平中，数据不包含CY和WY两个组，无法计算差异。")
                return
            
            diff_abundance = (group_means.loc['CY'] - group_means.loc['WY']).abs().sort_values(ascending=False)
            
            # 2. 筛选Top N差异特征
            num_features_to_select = min(self.top_n, len(diff_abundance))
            if num_features_to_select == 0:
                logging.warning(f"在 {level_name} 水平未找到任何特征数据，跳过。")
                return

            top_diff_features = diff_abundance.head(num_features_to_select).index.tolist()
            logging.info(f"在 {level_name} 水平筛选出 Top {num_features_to_select} 差异特征。")
            
            # 3. 提取丰度数据并进行Z-score标准化
            top_features_df = data_df[top_diff_features]
            zscore_df = top_features_df.apply(lambda x: (x - x.mean()) / x.std() if x.std() > 0 else 0, axis=0)
            
            # 4. 将列名更新为简称
            zscore_df.columns = [self._get_short_name(col) for col in zscore_df.columns]
            
            # 5. 合并并保存预处理数据
            sample_info = data_df[[self.sample_id_col, self.group_col]]
            final_data_df = pd.concat([sample_info, zscore_df], axis=1)
            
            output_csv_path = os.path.join(self.output_dir, f"heatmap_data_{level_name}.csv")
            final_data_df.to_csv(output_csv_path, index=False)
            logging.info(f"已保存用于热图的数据到: {output_csv_path}")

            # 6. 创建并保存热图（标准版和紧凑版）
            self._create_and_save_heatmap(final_data_df, level_name)
            self._create_compact_heatmap(final_data_df, level_name)

        except Exception as e:
            logging.error(f"处理文件 {file_path} 时发生错误: {e}")

    def create_multi_panel_figure(self):
        """
        创建多面板组合图，展示所有分类水平的热图
        适合期刊发表的综合图形
        """
        logging.info("创建多面板组合图...")

        # 收集所有处理过的数据
        all_data = {}
        for level_name in self.input_files.keys():
            csv_path = os.path.join(self.output_dir, f"heatmap_data_{level_name}.csv")
            if os.path.exists(csv_path):
                all_data[level_name] = pd.read_csv(csv_path)

        if not all_data:
            logging.warning("没有找到处理过的数据，跳过多面板图创建")
            return

        # 计算面板布局
        n_levels = len(all_data)
        if n_levels <= 3:
            nrows, ncols = 1, n_levels
            fig_width = self.current_config['double_column_width']
            fig_height = 3.0
        else:
            nrows, ncols = 2, 3
            fig_width = self.current_config['double_column_width']
            fig_height = 5.0

        # 创建多面板图
        fig, axes = plt.subplots(nrows, ncols, figsize=(fig_width, fig_height))
        if n_levels == 1:
            axes = [axes]
        elif nrows == 1:
            axes = axes.flatten()
        else:
            axes = axes.flatten()

        # 为每个分类水平创建子图
        for idx, (level_name, data_df) in enumerate(all_data.items()):
            if idx >= len(axes):
                break

            ax = axes[idx]

            # 准备数据
            sample_info = data_df[[self.sample_id_col, self.group_col]].set_index(self.sample_id_col)
            heatmap_data = data_df.drop(columns=[self.group_col]).set_index(self.sample_id_col)
            sorted_samples = sample_info.sort_values(by=self.group_col).index
            heatmap_data = heatmap_data.loc[sorted_samples]

            # 创建热图
            im = ax.imshow(heatmap_data.T,
                          cmap='RdBu_r',
                          aspect='auto',
                          vmin=-2, vmax=2)

            # 设置标签
            ax.set_title(level_name.capitalize(), fontsize=8, fontweight='bold')
            ax.set_xticks([])
            ax.set_yticks(range(min(5, len(heatmap_data.columns))))  # 最多显示5个特征标签
            if len(heatmap_data.columns) <= 5:
                ax.set_yticklabels(heatmap_data.columns, fontsize=6)
            else:
                ax.set_yticklabels([f'Feature {i+1}' for i in range(5)], fontsize=6)

            # 添加组别指示
            for group in sample_info.loc[sorted_samples, self.group_col].unique():
                group_samples = sample_info.loc[sorted_samples, self.group_col] == group
                start_pos = group_samples.idxmax()
                start_idx = sorted_samples.get_loc(start_pos)
                end_idx = start_idx + group_samples.sum() - 1
                mid_idx = (start_idx + end_idx) / 2

                ax.text(mid_idx, -0.3, group,
                       ha='center', va='top', fontsize=6, fontweight='bold',
                       color=self.group_colors[group])

        # 隐藏多余的子图
        for idx in range(len(all_data), len(axes)):
            axes[idx].set_visible(False)

        # 添加共同的颜色条
        cbar = fig.colorbar(im, ax=axes[:len(all_data)], shrink=0.8, aspect=30, pad=0.02)
        cbar.set_label('Z-score', fontsize=8)
        cbar.ax.tick_params(labelsize=6)

        # 总标题
        fig.suptitle('Differential Features Across Taxonomic Levels',
                    fontsize=10, fontweight='bold', y=0.95)

        # 调整布局
        plt.tight_layout()

        # 保存多面板图
        multi_panel_path = os.path.join(self.output_dir, 'multi_panel_heatmap.pdf')
        fig.savefig(multi_panel_path,
                   dpi=self.dpi,
                   bbox_inches='tight',
                   pad_inches=0.1,
                   format='pdf')
        logging.info(f"多面板组合图已保存: {multi_panel_path}")

        plt.close(fig)

    def run(self):
        """
        执行完整的期刊标准热图生成流程
        """
        logging.info("======= 开始生成期刊标准差异热图 =======")

        # 处理各个分类水平
        for level, path in self.input_files.items():
            self.process_level(level, path)

        # 创建多面板组合图
        self.create_multi_panel_figure()

        logging.info("======= 期刊标准热图生成完成 =======")
        logging.info(f"所有文件已保存至: {self.output_dir}")
        logging.info("输出包括:")
        logging.info("- 标准热图 (journal_heatmap_*.pdf/png/svg)")
        logging.info("- 紧凑热图 (compact_heatmap_*.pdf)")
        logging.info("- 多面板组合图 (multi_panel_heatmap.pdf)")
        logging.info("- 预处理数据 (heatmap_data_*.csv)")


if __name__ == "__main__":
    # --- 期刊标准配置 ---
    input_file_paths = {
        'phylum': 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y门.txt',
        'class': 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y纲.txt',
        'order': 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y目.txt',
        'family': 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y科.txt',
        'genus': 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y属.txt',
        'species': 'C:\\Users\\<USER>\\Desktop\\特征提取\\Y种.txt'
    }

    # 期刊标准参数
    config = {
        'top_n': 10,                    # 差异特征数量
        'figure_format': 'journal',     # 图形格式 ('journal', 'presentation')
        'dpi': 300,                     # 分辨率
        'output_base_dir': None,        # 输出目录（None=桌面）
    }

    try:
        logging.info("启动期刊标准差异热图生成器...")
        logging.info(f"配置参数: {config}")

        visualizer = DifferentialHeatmapVisualizer(
            input_files_dict=input_file_paths,
            top_n=config['top_n'],
            output_base_dir=config['output_base_dir'],
            figure_format=config['figure_format'],
            dpi=config['dpi']
        )

        visualizer.run()

        logging.info("✅ 期刊标准热图生成成功完成！")
        logging.info("📁 输出文件包括:")
        logging.info("   • PDF矢量图（完全可编辑）")
        logging.info("   • PNG高分辨率图（预览用）")
        logging.info("   • SVG矢量图（备选格式）")
        logging.info("   • 紧凑版PDF（小尺寸输出）")
        logging.info("   • 多面板组合图（综合展示）")
        logging.info("   • 预处理数据CSV（可重复分析）")

    except Exception as e:
        logging.critical(f"❌ 脚本执行失败: {e}")
        logging.critical("请检查输入文件路径和数据格式")
